{% extends "base.html" %}
{% load static %}

{% block page_name %}HOME{% endblock page_name %}

{% block content %}
<div class="container mx-auto pb-4 lg:pb-10 md:pb-10">
    <div class="rounded-xl h-25vh mt-16 mb-6 bg-cover bg-center -mx-40 md:mt-24 md:mb-10 md:h-50vh"
        style="background-image:url('{% static "img/main-container.jpg" %}');">
    </div>

    <div class="flex flex-wrap -mb-1 md:-mx-40">
        {% for room in object_list %}
        {% include 'mixins/room_card.html' with room=room %}
        {% endfor %}
    </div>

    <div class="flex items-center justify-center container mt-2 md:mt-0">
        <a {% if page_obj.has_previous %} href="?page={{ page_obj.previous_page_number }}" class="text-teal-500 visible"
            {% else %} class="invisible" {% endif %}>
            <i class="fas fa-arrow-left fa-lg"></i>
        </a>

        <span class="mx-3 font-medium text-lg -mt-1">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>

        <a {% if page_obj.has_next %} href="?page={{ page_obj.next_page_number }}" class="text-teal-500 visible"
            {% else %} class="invisible" {% endif %}>
            <i class="fas fa-arrow-right fa-lg"></i>
        </a>
    </div>
</div>
{% endblock content %}