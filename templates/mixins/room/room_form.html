<form method="POST" class="w-full" enctype="multipart/form-data">
    {% csrf_token %}

    {% if form.non_field_errors %}
        {% for error in form.non_field_errors %}
            <span class="text-red-700 font-medium text-sm">{{ error }}</span>
        {% endfor %}
    {% endif %}

    {% for field in form %}
        {% include 'mixins/room/room_input.html' with field=field %}
    {% endfor %}

    <button class="btn bg-red-500 text-white w-full">{{ cta }}</button>
</form> 