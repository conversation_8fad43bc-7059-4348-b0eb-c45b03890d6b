<footer
    class="w-full mx-auto text-center px-0 py-4 border-t font-medium text-gray-600 flex flex-col justify-between text-xs md:px-40 md:flex-row md:py-10 md:text-base">
    <div class="my-auto">
        &copy; 2020
        <a href="https://github.com/alstn2468" target="_blank"
            class="text-gray-700 font-bold transition-all duration-300 hover:text-blue-700">
            <PERSON><PERSON> <PERSON></a>. All rights reserved.
    </div>
    <div class="pt-3 lg:pt-0 md:pt-0">
        <a href="https://github.com/alstn2468" target="_blank"
            class="text-gray-500 hover:text-gray-700 transition-colors duration-300">
            <i class="fab fa-github-square text-xl md:text-3xl"></i>
        </a>
        <a href="https://www.facebook.com/profile.php?id=100003769223078" target="_blank"
            class="text-gray-500 hover:text-gray-700 transition-colors duration-300">
            <i class="fab fa-facebook-square text-xl md:text-3xl"></i>
        </a>
        <a href="https://www.instagram.com/minsu._.0102/" target="_blank"
            class="text-gray-500 hover:text-gray-700 transition-colors duration-300">
            <i class="fab fa-instagram-square text-xl md:text-3xl"></i>
        </a>
        <a href="https://www.linkedin.com/in/minsu-kim-336289160/" target="_blank"
            class="text-gray-500 hover:text-gray-700 transition-colors duration-300">
            <i class="fab fa-linkedin text-xl md:text-3xl"></i>
        </a>
        <a href="https://alstn2468.github.io/" target="_blank"
            class="text-gray-500 hover:text-gray-700 transition-colors duration-300">
            <i class="fas fa-rss-square text-xl md:text-3xl"></i>
        </a>
    </div>
</footer>