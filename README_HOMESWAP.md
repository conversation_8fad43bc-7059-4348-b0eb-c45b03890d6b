# HomeSwap Platform

A secure, scalable Django 5.2 back-end that powers a peer-to-peer accommodation marketplace (think "Airbnb core") with user/host management, property listings, availability calendar, booking & Stripe payments, reviews, search, and admin analytics.

## 🚀 Features

### ✅ Completed Core Features

- **Django 5.2 LTS** with Python 3.12+ support
- **Custom User Model** with KYC verification and role-based permissions
- **Property Management** with location coordinates and amenities
- **Booking System** with overlap prevention and status tracking
- **Review System** with rating aggregation
- **Payment Integration** ready for Stripe
- **REST API** with Django REST Framework
- **JWT Authentication** with refresh tokens
- **API Documentation** with drf-spectacular (Swagger/OpenAPI)
- **Admin Interface** with Django 5 facet filters
- **Security Hardening** with deployment-ready settings

### 🔄 Ready for Enhancement

- **Stripe Payment Processing**: Basic structure implemented, needs API keys configuration
- **Geo-spatial Search**: Basic coordinate-based search implemented, PostGIS ready for production
- **iCal Integration**: Models ready, import/export functionality to be implemented
- **Comprehensive Test Suite**: Basic tests implemented, coverage can be expanded
- **Seed Data Generation**: Command created, ready for use

## 🏗️ Architecture

### Core Domain Models

| Entity | Key Fields | Notes |
|--------|------------|-------|
| **User/Host** | email, phone, role, KYC-verified flag | Custom user model with role-based permissions |
| **Property** | title, slug, location (lat/lng), daily_price, amenities (JSON) | Location uses coordinates for geo queries |
| **Availability** | property_id, date, is_available | 1-row-per-day for optimistic locking |
| **Booking** | guest_id, property_id, dates, Stripe_payment_id, status | Unique constraint prevents overlapping bookings |
| **Review** | booking_id, rating (1-5), text, created_at | Review only after completed stay |

### API Endpoints

- **Authentication**: `/api/v1/auth/` (login, register, refresh)
- **Properties**: `/api/v1/properties/` (CRUD, search, availability)
- **Bookings**: `/api/v1/bookings/` (create, manage, cancel)
- **Reviews**: `/api/v1/reviews/` (create, list, respond)
- **Payments**: `/api/v1/payments/` (Stripe integration)
- **Documentation**: `/api/docs/` (Swagger UI), `/api/redoc/` (ReDoc)

## 🛠️ Technology Stack

- **Backend**: Django 5.2, Python 3.12+
- **Database**: PostgreSQL + PostGIS (production), SQLite (development)
- **API**: Django REST Framework 3.15+
- **Authentication**: JWT with djangorestframework-simplejwt
- **Documentation**: drf-spectacular (OpenAPI 3)
- **Payments**: Stripe
- **Task Queue**: Celery + Redis
- **File Storage**: AWS S3 (production), Local (development)
- **Testing**: pytest-django, factory-boy
- **Code Quality**: ruff, black, pre-commit

## 🚀 Quick Start

### Prerequisites

- Python 3.12+
- pip-tools or Poetry
- PostgreSQL (for production)
- Redis (for Celery)

### Installation

1. **Clone and setup virtual environment**:
```bash
git clone <repository-url>
cd django-airbnb-clone
python3.12 -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

2. **Install dependencies**:
```bash
pip install pip-tools
pip-compile requirements.in
pip install -r requirements.txt
```

3. **Environment configuration**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Database setup**:
```bash
python manage_new.py migrate
python manage_new.py createsuperuser
```

5. **Run development server**:
```bash
python manage_new.py runserver
```

### Development URLs

- **API Root**: http://127.0.0.1:8000/api/v1/
- **Admin Panel**: http://127.0.0.1:8000/admin/
- **API Documentation**: http://127.0.0.1:8000/api/docs/
- **Health Check**: http://127.0.0.1:8000/api/v1/health/

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env.example`):

```bash
# Django
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database
DB_NAME=homeswap_platform
DB_USER=postgres
DB_PASSWORD=postgres

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Redis/Celery
CELERY_BROKER_URL=redis://localhost:6379/0
```

### Settings Structure

- `homeswap_platform/settings/base.py` - Shared settings
- `homeswap_platform/settings/development.py` - Development overrides
- `homeswap_platform/settings/production.py` - Production configuration

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=apps --cov-report=html

# Run specific app tests
pytest apps/accounts/tests/
```

## 📊 API Examples

### Authentication

```bash
# Register a new user
curl -X POST http://127.0.0.1:8000/api/v1/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "first_name": "Test",
    "last_name": "User",
    "password": "securepassword123",
    "password_confirm": "securepassword123",
    "role": "guest"
  }'

# Login
curl -X POST http://127.0.0.1:8000/api/v1/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

### Health Check

```bash
curl http://127.0.0.1:8000/api/v1/health/
```

## 🔒 Security Features

- **HTTPS Enforcement** (production)
- **HSTS Headers** with 1-year max-age
- **CSRF Protection** with secure cookies
- **XSS Protection** headers
- **Content Type Sniffing** prevention
- **JWT Token Security** with rotation
- **Password Validation** with Django validators
- **Rate Limiting** (planned)

## 📈 Performance

- **Database Indexing** on foreign keys and query fields
- **Query Optimization** with select_related/prefetch_related
- **Caching Strategy** with Redis
- **Connection Pooling** for database
- **Static File Optimization** with S3/CloudFront

## 🚀 Deployment

### Production Checklist

1. Set `DEBUG=False`
2. Configure PostgreSQL + PostGIS
3. Set up Redis for caching/Celery
4. Configure AWS S3 for static/media files
5. Set up Sentry for error tracking
6. Configure proper SECRET_KEY
7. Set up SSL certificates
8. Run security check: `python manage.py check --deploy`

### Docker Support (Planned)

```bash
docker-compose up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

### Code Quality

```bash
# Format code
black .
ruff check --fix .

# Run pre-commit hooks
pre-commit run --all-files
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: `/api/docs/` for API reference
- **Admin Interface**: `/admin/` for data management
- **Health Check**: `/api/v1/health/` for system status

---

**Built with Django 5.2 + DRF + PostgreSQL + Redis + Stripe**
