#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile requirements.in
#
amqp==5.3.1
    # via kombu
asgiref==3.8.1
    # via
    #   django
    #   django-cors-headers
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
billiard==4.2.1
    # via celery
black==24.10.0
    # via -r requirements.in
boto3==1.35.36
    # via -r requirements.in
botocore==1.35.99
    # via
    #   boto3
    #   s3transfer
celery==5.4.0
    # via -r requirements.in
certifi==2025.4.26
    # via
    #   requests
    #   sentry-sdk
cfgv==3.4.0
    # via pre-commit
charset-normalizer==3.4.2
    # via requests
click==8.1.8
    # via
    #   black
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
click-didyoumean==0.3.1
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
coverage[toml]==7.8.2
    # via pytest-cov
distlib==0.3.9
    # via virtualenv
django==5.2
    # via
    #   -r requirements.in
    #   django-cors-headers
    #   django-debug-toolbar
    #   django-extensions
    #   django-ical
    #   django-recurrence
    #   django-storages
    #   djangorestframework
    #   djangorestframework-simplejwt
    #   drf-spectacular
django-cors-headers==4.4.0
    # via -r requirements.in
django-debug-toolbar==4.4.6
    # via -r requirements.in
django-environ==0.11.2
    # via -r requirements.in
django-extensions==3.2.3
    # via -r requirements.in
django-ical==1.9.2
    # via -r requirements.in
django-recurrence==1.11.1
    # via django-ical
django-storages==1.14.4
    # via -r requirements.in
djangorestframework==3.15.2
    # via
    #   -r requirements.in
    #   djangorestframework-simplejwt
    #   drf-spectacular
djangorestframework-simplejwt==5.3.0
    # via -r requirements.in
drf-spectacular==0.27.2
    # via -r requirements.in
factory-boy==3.3.1
    # via -r requirements.in
faker==37.3.0
    # via factory-boy
filelock==3.18.0
    # via virtualenv
icalendar==5.0.13
    # via
    #   -r requirements.in
    #   django-ical
identify==2.6.12
    # via pre-commit
idna==3.10
    # via requests
inflection==0.5.1
    # via drf-spectacular
iniconfig==2.1.0
    # via pytest
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
jsonschema==4.24.0
    # via drf-spectacular
jsonschema-specifications==2025.4.1
    # via jsonschema
kombu==5.5.4
    # via celery
mypy-extensions==1.1.0
    # via black
nodeenv==1.9.1
    # via pre-commit
packaging==25.0
    # via
    #   black
    #   kombu
    #   pytest
pathspec==0.12.1
    # via black
pillow==10.4.0
    # via -r requirements.in
platformdirs==4.3.8
    # via
    #   black
    #   virtualenv
pluggy==1.6.0
    # via pytest
pre-commit==3.8.0
    # via -r requirements.in
prompt-toolkit==3.0.51
    # via click-repl
psycopg2-binary==2.9.9
    # via -r requirements.in
pyjwt==2.10.1
    # via djangorestframework-simplejwt
pytest==8.3.3
    # via
    #   -r requirements.in
    #   pytest-cov
    #   pytest-django
pytest-cov==5.0.0
    # via -r requirements.in
pytest-django==4.9.0
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via
    #   botocore
    #   celery
    #   django-recurrence
    #   icalendar
python-decouple==3.8
    # via -r requirements.in
pytz==2025.2
    # via icalendar
pyyaml==6.0.2
    # via
    #   drf-spectacular
    #   pre-commit
redis==5.1.1
    # via -r requirements.in
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via stripe
rpds-py==0.25.1
    # via
    #   jsonschema
    #   referencing
ruff==0.6.9
    # via -r requirements.in
s3transfer==0.10.4
    # via boto3
sentry-sdk==2.17.0
    # via -r requirements.in
six==1.17.0
    # via python-dateutil
sqlparse==0.5.3
    # via
    #   django
    #   django-debug-toolbar
stripe==10.12.0
    # via -r requirements.in
typing-extensions==4.14.0
    # via
    #   referencing
    #   stripe
tzdata==2025.2
    # via
    #   celery
    #   faker
    #   kombu
uritemplate==4.2.0
    # via drf-spectacular
urllib3==2.4.0
    # via
    #   botocore
    #   requests
    #   sentry-sdk
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
virtualenv==20.31.2
    # via pre-commit
wcwidth==0.2.13
    # via prompt-toolkit
