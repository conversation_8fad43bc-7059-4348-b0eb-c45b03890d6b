"""
Admin configuration for accounts app.
"""

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _

from .models import User, UserVerification


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Custom User admin with Django 5 facet filters.
    """
    list_display = [
        'email', 'username', 'first_name', 'last_name',
        'role', 'is_kyc_verified', 'email_verified', 'is_active'
    ]
    list_filter = [
        'role', 'is_kyc_verified', 'email_verified', 'phone_verified',
        'is_active', 'is_staff', 'date_joined'
    ]
    search_fields = ['email', 'username', 'first_name', 'last_name', 'phone']
    ordering = ['-date_joined']
    
    # Django 5 facet filters for fast moderation
    list_facets = ['role', 'is_kyc_verified', 'email_verified']
    
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('Personal info'), {
            'fields': (
                'first_name', 'last_name', 'email', 'phone',
                'avatar', 'bio', 'date_of_birth', 'address'
            )
        }),
        (_('Role & Verification'), {
            'fields': (
                'role', 'is_kyc_verified', 'email_verified', 'phone_verified'
            )
        }),
        (_('Emergency Contact'), {
            'fields': ('emergency_contact_name', 'emergency_contact_phone'),
            'classes': ('collapse',)
        }),
        (_('Permissions'), {
            'fields': (
                'is_active', 'is_staff', 'is_superuser',
                'groups', 'user_permissions'
            ),
            'classes': ('collapse',)
        }),
        (_('Important dates'), {
            'fields': ('last_login', 'date_joined'),
            'classes': ('collapse',)
        }),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': (
                'username', 'email', 'first_name', 'last_name',
                'role', 'password1', 'password2'
            ),
        }),
    )
    
    readonly_fields = ['date_joined', 'last_login']


@admin.register(UserVerification)
class UserVerificationAdmin(admin.ModelAdmin):
    """
    Admin for user verification tracking.
    """
    list_display = [
        'user', 'verification_type', 'is_verified',
        'created_at', 'verified_at', 'expires_at'
    ]
    list_filter = ['verification_type', 'is_verified', 'created_at']
    search_fields = ['user__email', 'user__username', 'token']
    readonly_fields = ['token', 'created_at', 'verified_at']
    
    # Django 5 facet filters
    list_facets = ['verification_type', 'is_verified']
