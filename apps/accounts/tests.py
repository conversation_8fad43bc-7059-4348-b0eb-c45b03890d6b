"""
Tests for the accounts app.
"""

import pytest
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

User = get_user_model()


class UserModelTest(APITestCase):
    """Test the custom User model."""
    
    def test_create_user(self):
        """Test creating a user with email."""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.role, User.Role.GUEST)
        self.assertFalse(user.is_kyc_verified)
        self.assertTrue(user.check_password('testpass123'))
    
    def test_create_host_user(self):
        """Test creating a host user."""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='hostuser',
            first_name='Host',
            last_name='User',
            password='testpass123',
            role=User.Role.HOST
        )
        
        self.assertEqual(user.role, User.Role.HOST)
        self.assertTrue(user.is_host)
        self.assertFalse(user.is_guest)
    
    def test_user_can_host_properties(self):
        """Test user can host properties only if KYC verified host."""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='hostuser',
            password='testpass123',
            role=User.Role.HOST
        )
        
        # Not KYC verified yet
        self.assertFalse(user.can_host_properties())
        
        # After KYC verification
        user.is_kyc_verified = True
        user.save()
        self.assertTrue(user.can_host_properties())


class UserRegistrationTest(APITestCase):
    """Test user registration API."""
    
    def setUp(self):
        self.register_url = reverse('accounts:register')
    
    def test_register_user_success(self):
        """Test successful user registration."""
        data = {
            'email': '<EMAIL>',
            'username': 'newuser',
            'first_name': 'New',
            'last_name': 'User',
            'password': 'securepass123',
            'password_confirm': 'securepass123',
            'role': 'guest'
        }
        
        response = self.client.post(self.register_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())
    
    def test_register_user_password_mismatch(self):
        """Test registration with password mismatch."""
        data = {
            'email': '<EMAIL>',
            'username': 'newuser',
            'first_name': 'New',
            'last_name': 'User',
            'password': 'securepass123',
            'password_confirm': 'differentpass',
            'role': 'guest'
        }
        
        response = self.client.post(self.register_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(User.objects.filter(email='<EMAIL>').exists())


class UserAuthenticationTest(APITestCase):
    """Test user authentication API."""
    
    def setUp(self):
        self.login_url = reverse('accounts:login')
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
    
    def test_login_success(self):
        """Test successful login."""
        data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
    
    def test_login_invalid_credentials(self):
        """Test login with invalid credentials."""
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(self.login_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class UserProfileTest(APITestCase):
    """Test user profile API."""
    
    def setUp(self):
        self.profile_url = reverse('accounts:profile')
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
    
    def test_get_profile_authenticated(self):
        """Test getting profile when authenticated."""
        self.client.force_authenticate(user=self.user)
        
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], '<EMAIL>')
        self.assertEqual(response.data['username'], 'testuser')
    
    def test_get_profile_unauthenticated(self):
        """Test getting profile when not authenticated."""
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_update_profile(self):
        """Test updating user profile."""
        self.client.force_authenticate(user=self.user)
        
        data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'bio': 'This is my bio'
        }
        
        response = self.client.patch(self.profile_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.first_name, 'Updated')
        self.assertEqual(self.user.last_name, 'Name')
        self.assertEqual(self.user.bio, 'This is my bio')
