"""
User models for HomeSwap Platform.
"""

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator


class User(AbstractUser):
    """
    Custom User model with additional fields for HomeSwap Platform.
    """
    
    class Role(models.TextChoices):
        GUEST = 'guest', _('Guest')
        HOST = 'host', _('Host')
        ADMIN = 'admin', _('Admin')
    
    email = models.EmailField(_('email address'), unique=True)
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message=_("Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")
    )
    phone = models.CharField(
        _('phone number'),
        validators=[phone_regex],
        max_length=17,
        blank=True
    )
    role = models.CharField(
        _('role'),
        max_length=10,
        choices=Role.choices,
        default=Role.GUEST
    )
    is_kyc_verified = models.BooleanField(
        _('KYC verified'),
        default=False,
        help_text=_('Designates whether this user has completed KYC verification.')
    )
    avatar = models.ImageField(
        _('avatar'),
        upload_to='avatars/',
        blank=True,
        null=True
    )
    bio = models.TextField(
        _('bio'),
        max_length=500,
        blank=True
    )
    date_of_birth = models.DateField(
        _('date of birth'),
        blank=True,
        null=True
    )
    address = models.TextField(
        _('address'),
        blank=True
    )
    emergency_contact_name = models.CharField(
        _('emergency contact name'),
        max_length=100,
        blank=True
    )
    emergency_contact_phone = models.CharField(
        _('emergency contact phone'),
        validators=[phone_regex],
        max_length=17,
        blank=True
    )
    email_verified = models.BooleanField(
        _('email verified'),
        default=False
    )
    phone_verified = models.BooleanField(
        _('phone verified'),
        default=False
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']
    
    class Meta:
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['role']),
            models.Index(fields=['is_kyc_verified']),
        ]
    
    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"
    
    @property
    def is_host(self):
        return self.role == self.Role.HOST
    
    @property
    def is_guest(self):
        return self.role == self.Role.GUEST
    
    def can_host_properties(self):
        """Check if user can host properties (must be KYC verified host)."""
        return self.is_host and self.is_kyc_verified


class UserVerification(models.Model):
    """
    Model to track user verification processes.
    """
    
    class VerificationType(models.TextChoices):
        EMAIL = 'email', _('Email')
        PHONE = 'phone', _('Phone')
        IDENTITY = 'identity', _('Identity')
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='verifications'
    )
    verification_type = models.CharField(
        max_length=10,
        choices=VerificationType.choices
    )
    token = models.CharField(max_length=255)
    is_verified = models.BooleanField(default=False)
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    verified_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        verbose_name = _('User Verification')
        verbose_name_plural = _('User Verifications')
        unique_together = ['user', 'verification_type']
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.verification_type}"
