# Generated by Django 5.2 on 2025-06-04 01:44

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.Char<PERSON>ield(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=254, unique=True, verbose_name="email address"
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True,
                        max_length=17,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                        verbose_name="phone number",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("guest", "Guest"),
                            ("host", "Host"),
                            ("admin", "Admin"),
                        ],
                        default="guest",
                        max_length=10,
                        verbose_name="role",
                    ),
                ),
                (
                    "is_kyc_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether this user has completed KYC verification.",
                        verbose_name="KYC verified",
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="avatars/",
                        verbose_name="avatar",
                    ),
                ),
                (
                    "bio",
                    models.TextField(blank=True, max_length=500, verbose_name="bio"),
                ),
                (
                    "date_of_birth",
                    models.DateField(
                        blank=True, null=True, verbose_name="date of birth"
                    ),
                ),
                ("address", models.TextField(blank=True, verbose_name="address")),
                (
                    "emergency_contact_name",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        verbose_name="emergency contact name",
                    ),
                ),
                (
                    "emergency_contact_phone",
                    models.CharField(
                        blank=True,
                        max_length=17,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                        verbose_name="emergency contact phone",
                    ),
                ),
                (
                    "email_verified",
                    models.BooleanField(default=False, verbose_name="email verified"),
                ),
                (
                    "phone_verified",
                    models.BooleanField(default=False, verbose_name="phone verified"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "User",
                "verbose_name_plural": "Users",
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="UserVerification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "verification_type",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("phone", "Phone"),
                            ("identity", "Identity"),
                        ],
                        max_length=10,
                    ),
                ),
                ("token", models.CharField(max_length=255)),
                ("is_verified", models.BooleanField(default=False)),
                ("expires_at", models.DateTimeField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("verified_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="verifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Verification",
                "verbose_name_plural": "User Verifications",
            },
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["email"], name="accounts_us_email_74c8d6_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["role"], name="accounts_us_role_1fa9a5_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(
                fields=["is_kyc_verified"], name="accounts_us_is_kyc__7e3cfe_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="userverification",
            index=models.Index(fields=["token"], name="accounts_us_token_5c7900_idx"),
        ),
        migrations.AddIndex(
            model_name="userverification",
            index=models.Index(
                fields=["expires_at"], name="accounts_us_expires_064b10_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="userverification",
            unique_together={("user", "verification_type")},
        ),
    ]
