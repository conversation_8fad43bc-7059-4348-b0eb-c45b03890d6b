"""
Management command to seed the database with sample data.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
import random
from datetime import date, timedelta

from properties.models import Property, PropertyImage, Availability
from bookings.models import Booking
from reviews.models import Review

User = get_user_model()


class Command(BaseCommand):
    help = 'Seed the database with sample data for development'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--hosts',
            type=int,
            default=20,
            help='Number of host users to create'
        )
        parser.add_argument(
            '--guests',
            type=int,
            default=50,
            help='Number of guest users to create'
        )
        parser.add_argument(
            '--properties',
            type=int,
            default=100,
            help='Number of properties to create'
        )
        parser.add_argument(
            '--bookings',
            type=int,
            default=300,
            help='Number of bookings to create'
        )
    
    def handle(self, *args, **options):
        self.stdout.write('Starting data seeding...')
        
        # Create users
        hosts = self.create_hosts(options['hosts'])
        guests = self.create_guests(options['guests'])
        
        # Create properties
        properties = self.create_properties(hosts, options['properties'])
        
        # Create availability
        self.create_availability(properties)
        
        # Create bookings
        bookings = self.create_bookings(guests, properties, options['bookings'])
        
        # Create reviews
        self.create_reviews(bookings)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully seeded database with:\n'
                f'- {len(hosts)} hosts\n'
                f'- {len(guests)} guests\n'
                f'- {len(properties)} properties\n'
                f'- {len(bookings)} bookings'
            )
        )
    
    def create_hosts(self, count):
        """Create host users."""
        hosts = []
        for i in range(count):
            host = User.objects.create_user(
                email=f'host{i+1}@homeswap.com',
                username=f'host{i+1}',
                first_name=f'Host{i+1}',
                last_name='User',
                password='password123',
                role=User.Role.HOST,
                is_kyc_verified=True,
                email_verified=True,
                bio=f'Experienced host with {random.randint(1, 10)} years of hosting experience.',
                phone=f'+1555{random.randint(1000000, 9999999)}'
            )
            hosts.append(host)
        
        self.stdout.write(f'Created {count} host users')
        return hosts
    
    def create_guests(self, count):
        """Create guest users."""
        guests = []
        for i in range(count):
            guest = User.objects.create_user(
                email=f'guest{i+1}@homeswap.com',
                username=f'guest{i+1}',
                first_name=f'Guest{i+1}',
                last_name='User',
                password='password123',
                role=User.Role.GUEST,
                email_verified=True,
                bio=f'Travel enthusiast who loves exploring new places.',
                phone=f'+1555{random.randint(1000000, 9999999)}'
            )
            guests.append(guest)
        
        self.stdout.write(f'Created {count} guest users')
        return guests
    
    def create_properties(self, hosts, count):
        """Create properties."""
        property_types = [choice[0] for choice in Property.PropertyType.choices]
        cities = [
            ('New York', 'NY', 'USA', 40.7128, -74.0060),
            ('Los Angeles', 'CA', 'USA', 34.0522, -118.2437),
            ('Chicago', 'IL', 'USA', 41.8781, -87.6298),
            ('Miami', 'FL', 'USA', 25.7617, -80.1918),
            ('San Francisco', 'CA', 'USA', 37.7749, -122.4194),
            ('Seattle', 'WA', 'USA', 47.6062, -122.3321),
            ('Austin', 'TX', 'USA', 30.2672, -97.7431),
            ('Denver', 'CO', 'USA', 39.7392, -104.9903),
        ]
        
        amenities_options = [
            'WiFi', 'Kitchen', 'Washer', 'Dryer', 'Air conditioning',
            'Heating', 'TV', 'Hot tub', 'Pool', 'Gym', 'Parking',
            'Pets allowed', 'Smoking allowed', 'Wheelchair accessible',
            'Breakfast', 'Laptop friendly workspace'
        ]
        
        properties = []
        for i in range(count):
            host = random.choice(hosts)
            city_data = random.choice(cities)
            city, state, country, base_lat, base_lng = city_data
            
            # Add some randomness to coordinates
            lat = base_lat + random.uniform(-0.1, 0.1)
            lng = base_lng + random.uniform(-0.1, 0.1)
            
            property_obj = Property.objects.create(
                host=host,
                title=f'Beautiful {random.choice(property_types)} in {city}',
                description=f'A wonderful place to stay in {city}. Perfect for travelers looking for comfort and convenience.',
                property_type=random.choice(property_types),
                latitude=Decimal(str(round(lat, 6))),
                longitude=Decimal(str(round(lng, 6))),
                address=f'{random.randint(100, 9999)} {random.choice(["Main", "Oak", "Pine", "Elm", "Cedar"])} St',
                city=city,
                state=state,
                country=country,
                postal_code=f'{random.randint(10000, 99999)}',
                bedrooms=random.randint(1, 5),
                bathrooms=random.choice([1, 1.5, 2, 2.5, 3, 3.5, 4]),
                max_guests=random.randint(2, 10),
                square_feet=random.randint(500, 3000),
                daily_price=Decimal(str(random.randint(50, 500))),
                cleaning_fee=Decimal(str(random.randint(25, 100))),
                security_deposit=Decimal(str(random.randint(100, 500))),
                amenities=random.sample(amenities_options, random.randint(3, 8)),
                house_rules=['No smoking', 'No parties', 'Check-in after 3 PM'],
                minimum_stay=random.randint(1, 7),
                maximum_stay=random.randint(14, 365),
                status=Property.Status.ACTIVE,
                is_instant_book=random.choice([True, False])
            )
            properties.append(property_obj)
        
        self.stdout.write(f'Created {count} properties')
        return properties
    
    def create_availability(self, properties):
        """Create availability records for properties."""
        start_date = date.today()
        end_date = start_date + timedelta(days=365)
        
        for property_obj in properties:
            current_date = start_date
            while current_date <= end_date:
                # 90% chance of being available
                is_available = random.random() > 0.1
                
                # Occasional price overrides
                price_override = None
                if random.random() < 0.1:  # 10% chance
                    price_override = property_obj.daily_price * Decimal(str(random.uniform(0.8, 1.5)))
                
                Availability.objects.create(
                    property=property_obj,
                    date=current_date,
                    is_available=is_available,
                    price_override=price_override
                )
                
                current_date += timedelta(days=1)
        
        self.stdout.write('Created availability records')
    
    def create_bookings(self, guests, properties, count):
        """Create bookings."""
        bookings = []
        attempts = 0
        max_attempts = count * 3  # Allow more attempts to find non-overlapping dates

        while len(bookings) < count and attempts < max_attempts:
            attempts += 1
            guest = random.choice(guests)
            property_obj = random.choice(properties)

            # Random check-in date in the past or future
            days_offset = random.randint(-180, 180)
            check_in = date.today() + timedelta(days=days_offset)
            nights = random.randint(1, 14)
            check_out = check_in + timedelta(days=nights)

            # Skip if dates overlap with existing bookings
            if Booking.objects.filter(
                property=property_obj,
                check_in_date__lt=check_out,
                check_out_date__gt=check_in,
                status__in=[Booking.Status.CONFIRMED, Booking.Status.PENDING]
            ).exists():
                continue
            
            # Calculate pricing
            subtotal = property_obj.daily_price * nights
            cleaning_fee = property_obj.cleaning_fee
            service_fee = (subtotal * Decimal('0.03')).quantize(Decimal('0.01'))
            taxes = ((subtotal + service_fee) * Decimal('0.08')).quantize(Decimal('0.01'))
            total_amount = (subtotal + cleaning_fee + service_fee + taxes).quantize(Decimal('0.01'))
            
            # Random status
            if check_in < date.today():
                status = random.choice([Booking.Status.COMPLETED, Booking.Status.CANCELLED])
            else:
                status = random.choice([Booking.Status.CONFIRMED, Booking.Status.PENDING])
            
            booking = Booking.objects.create(
                guest=guest,
                property=property_obj,
                check_in_date=check_in,
                check_out_date=check_out,
                number_of_guests=random.randint(1, property_obj.max_guests),
                nightly_rate=property_obj.daily_price,
                number_of_nights=nights,
                subtotal=subtotal,
                cleaning_fee=cleaning_fee,
                service_fee=service_fee,
                taxes=taxes,
                total_amount=total_amount,
                status=status,
                special_requests=random.choice([
                    '', 'Early check-in please', 'Late check-out needed',
                    'Quiet room preferred', 'Ground floor preferred'
                ])
            )
            
            if status == Booking.Status.CONFIRMED:
                booking.confirmed_at = timezone.now()
                booking.save()
            
            bookings.append(booking)
        
        self.stdout.write(f'Created {len(bookings)} bookings')
        return bookings
    
    def create_reviews(self, bookings):
        """Create reviews for completed bookings."""
        completed_bookings = [b for b in bookings if b.status == Booking.Status.COMPLETED]
        
        for booking in completed_bookings:
            # 80% chance of having a review
            if random.random() < 0.8:
                rating = random.randint(3, 5)  # Mostly positive reviews
                
                comments = [
                    "Great place to stay! Highly recommended.",
                    "Clean and comfortable. Host was very responsive.",
                    "Perfect location and amenities. Will book again!",
                    "Exactly as described. Had a wonderful time.",
                    "Good value for money. Nice neighborhood.",
                    "Host was very accommodating. Great experience overall."
                ]
                
                Review.objects.create(
                    booking=booking,
                    reviewer=booking.guest,
                    property=booking.property,
                    rating=rating,
                    cleanliness_rating=random.randint(rating-1, 5),
                    accuracy_rating=random.randint(rating-1, 5),
                    communication_rating=random.randint(rating-1, 5),
                    location_rating=random.randint(rating-1, 5),
                    check_in_rating=random.randint(rating-1, 5),
                    value_rating=random.randint(rating-1, 5),
                    title=f"{'Great' if rating >= 4 else 'Good'} stay in {booking.property.city}",
                    comment=random.choice(comments),
                    would_recommend=rating >= 4
                )
        
        self.stdout.write(f'Created reviews for completed bookings')
