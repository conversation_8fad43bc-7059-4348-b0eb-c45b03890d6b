"""
Core models for HomeSwap Platform.
"""

from django.db import models
from django.utils.translation import gettext_lazy as _


class TimeStampedModel(models.Model):
    """
    Abstract base class for models that need created_at and updated_at fields.
    """
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    
    class Meta:
        abstract = True


class SoftDeleteModel(models.Model):
    """
    Abstract base class for models that need soft delete functionality.
    """
    is_deleted = models.BooleanField(_('is deleted'), default=False)
    deleted_at = models.DateTimeField(_('deleted at'), blank=True, null=True)
    
    class Meta:
        abstract = True


class SEOModel(models.Model):
    """
    Abstract base class for models that need SEO fields.
    """
    meta_title = models.CharField(
        _('meta title'),
        max_length=60,
        blank=True,
        help_text=_('SEO meta title (max 60 characters)')
    )
    meta_description = models.Char<PERSON><PERSON>(
        _('meta description'),
        max_length=160,
        blank=True,
        help_text=_('SEO meta description (max 160 characters)')
    )
    meta_keywords = models.CharField(
        _('meta keywords'),
        max_length=255,
        blank=True,
        help_text=_('SEO meta keywords (comma-separated)')
    )
    
    class Meta:
        abstract = True
