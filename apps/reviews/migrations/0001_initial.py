# Generated by Django 5.2 on 2025-06-04 01:44

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("bookings", "0001_initial"),
        ("properties", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Review",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "rating",
                    models.PositiveIntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="overall rating",
                    ),
                ),
                (
                    "cleanliness_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="cleanliness rating",
                    ),
                ),
                (
                    "accuracy_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="accuracy rating",
                    ),
                ),
                (
                    "communication_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="communication rating",
                    ),
                ),
                (
                    "location_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="location rating",
                    ),
                ),
                (
                    "check_in_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="check-in rating",
                    ),
                ),
                (
                    "value_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="value rating",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="review title"
                    ),
                ),
                (
                    "comment",
                    models.TextField(
                        help_text="Share your experience with future guests",
                        verbose_name="review comment",
                    ),
                ),
                (
                    "would_recommend",
                    models.BooleanField(default=True, verbose_name="would recommend"),
                ),
                (
                    "is_published",
                    models.BooleanField(default=True, verbose_name="is published"),
                ),
                (
                    "is_flagged",
                    models.BooleanField(default=False, verbose_name="is flagged"),
                ),
                (
                    "moderation_notes",
                    models.TextField(
                        blank=True,
                        help_text="Internal notes for moderation",
                        verbose_name="moderation notes",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "booking",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="review",
                        to="bookings.booking",
                    ),
                ),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reviews",
                        to="properties.property",
                    ),
                ),
                (
                    "reviewer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reviews_given",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Review",
                "verbose_name_plural": "Reviews",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ReviewHelpfulness",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_helpful", models.BooleanField(verbose_name="is helpful")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "review",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="helpfulness_votes",
                        to="reviews.review",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="review_helpfulness_votes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Review Helpfulness",
                "verbose_name_plural": "Review Helpfulness Votes",
            },
        ),
        migrations.CreateModel(
            name="ReviewResponse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "response",
                    models.TextField(
                        help_text="Host response to the guest review",
                        verbose_name="host response",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "host",
                    models.ForeignKey(
                        limit_choices_to={"role": "host"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="review_responses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "review",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="host_response",
                        to="reviews.review",
                    ),
                ),
            ],
            options={
                "verbose_name": "Review Response",
                "verbose_name_plural": "Review Responses",
            },
        ),
        migrations.AddIndex(
            model_name="review",
            index=models.Index(
                fields=["property", "is_published"],
                name="reviews_rev_propert_e9ea18_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="review",
            index=models.Index(
                fields=["reviewer"], name="reviews_rev_reviewe_e817a5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="review",
            index=models.Index(fields=["rating"], name="reviews_rev_rating_2db6dd_idx"),
        ),
        migrations.AddIndex(
            model_name="review",
            index=models.Index(
                fields=["created_at"], name="reviews_rev_created_bdcc91_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="reviewhelpfulness",
            index=models.Index(
                fields=["review", "is_helpful"], name="reviews_rev_review__6a0b5a_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="reviewhelpfulness",
            unique_together={("review", "user")},
        ),
        migrations.AddIndex(
            model_name="reviewresponse",
            index=models.Index(
                fields=["review"], name="reviews_rev_review__989a48_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="reviewresponse",
            index=models.Index(fields=["host"], name="reviews_rev_host_id_c09026_idx"),
        ),
        migrations.AddIndex(
            model_name="reviewresponse",
            index=models.Index(
                fields=["created_at"], name="reviews_rev_created_eb396a_idx"
            ),
        ),
    ]
