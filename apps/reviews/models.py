"""
Review models for HomeSwap Platform.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError

User = get_user_model()


class Review(models.Model):
    """
    Review model for properties - only after completed stay.
    """
    booking = models.OneToOneField(
        'bookings.Booking',
        on_delete=models.CASCADE,
        related_name='review'
    )
    reviewer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='reviews_given'
    )
    property = models.ForeignKey(
        'properties.Property',
        on_delete=models.CASCADE,
        related_name='reviews'
    )
    
    # Overall rating (1-5 stars)
    rating = models.PositiveIntegerField(
        _('overall rating'),
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    
    # Detailed ratings
    cleanliness_rating = models.PositiveIntegerField(
        _('cleanliness rating'),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        blank=True,
        null=True
    )
    accuracy_rating = models.PositiveIntegerField(
        _('accuracy rating'),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        blank=True,
        null=True
    )
    communication_rating = models.PositiveIntegerField(
        _('communication rating'),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        blank=True,
        null=True
    )
    location_rating = models.PositiveIntegerField(
        _('location rating'),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        blank=True,
        null=True
    )
    check_in_rating = models.PositiveIntegerField(
        _('check-in rating'),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        blank=True,
        null=True
    )
    value_rating = models.PositiveIntegerField(
        _('value rating'),
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        blank=True,
        null=True
    )
    
    # Review text
    title = models.CharField(
        _('review title'),
        max_length=200,
        blank=True
    )
    comment = models.TextField(
        _('review comment'),
        help_text=_('Share your experience with future guests')
    )
    
    # Recommendations
    would_recommend = models.BooleanField(
        _('would recommend'),
        default=True
    )
    
    # Moderation
    is_published = models.BooleanField(
        _('is published'),
        default=True
    )
    is_flagged = models.BooleanField(
        _('is flagged'),
        default=False
    )
    moderation_notes = models.TextField(
        _('moderation notes'),
        blank=True,
        help_text=_('Internal notes for moderation')
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Review')
        verbose_name_plural = _('Reviews')
        indexes = [
            models.Index(fields=['property', 'is_published']),
            models.Index(fields=['reviewer']),
            models.Index(fields=['rating']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Review by {self.reviewer.get_full_name()} for {self.property.title}"
    
    def clean(self):
        """Validate that review can only be created after completed stay."""
        if self.booking:
            # Check if booking is completed
            if self.booking.status != 'completed':
                raise ValidationError(_('Reviews can only be created for completed bookings.'))
            
            # Check if reviewer is the guest from the booking
            if self.reviewer != self.booking.guest:
                raise ValidationError(_('Only the guest from the booking can create a review.'))
            
            # Ensure property matches booking property
            if self.property != self.booking.property:
                raise ValidationError(_('Review property must match booking property.'))
    
    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
    
    @property
    def host(self):
        """Get the host of the reviewed property."""
        return self.property.host


class ReviewResponse(models.Model):
    """
    Host response to a guest review.
    """
    review = models.OneToOneField(
        Review,
        on_delete=models.CASCADE,
        related_name='host_response'
    )
    host = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='review_responses',
        limit_choices_to={'role': 'host'}
    )
    response = models.TextField(
        _('host response'),
        help_text=_('Host response to the guest review')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Review Response')
        verbose_name_plural = _('Review Responses')
        indexes = [
            models.Index(fields=['review']),
            models.Index(fields=['host']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Response by {self.host.get_full_name()} to review {self.review.id}"
    
    def clean(self):
        """Validate that only the property host can respond."""
        if self.review and self.host != self.review.property.host:
            raise ValidationError(_('Only the property host can respond to reviews.'))
    
    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)


class ReviewHelpfulness(models.Model):
    """
    Track if users find reviews helpful.
    """
    review = models.ForeignKey(
        Review,
        on_delete=models.CASCADE,
        related_name='helpfulness_votes'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='review_helpfulness_votes'
    )
    is_helpful = models.BooleanField(_('is helpful'))
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = _('Review Helpfulness')
        verbose_name_plural = _('Review Helpfulness Votes')
        unique_together = ['review', 'user']
        indexes = [
            models.Index(fields=['review', 'is_helpful']),
        ]
    
    def __str__(self):
        helpful_text = "helpful" if self.is_helpful else "not helpful"
        return f"{self.user.get_full_name()} found review {self.review.id} {helpful_text}"
