"""
Views for the bookings app.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter
from django.db.models import Q
from django.utils import timezone
from drf_spectacular.utils import extend_schema

from .models import Booking, BookingMessage
from .serializers import (
    BookingListSerializer,
    BookingDetailSerializer,
    BookingCreateSerializer,
    BookingMessageSerializer,
    BookingCancellationSerializer
)


class BookingViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing bookings.
    """
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['status', 'property__city', 'check_in_date']
    ordering_fields = ['check_in_date', 'created_at', 'total_amount']
    ordering = ['-created_at']
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter bookings based on user role."""
        user = self.request.user
        
        if self.action == 'host_bookings':
            # Host can see bookings for their properties
            return Booking.objects.filter(
                property__host=user
            ).select_related('guest', 'property').prefetch_related('messages')
        else:
            # Guests can see their own bookings
            return Booking.objects.filter(
                guest=user
            ).select_related('property', 'property__host').prefetch_related('messages')
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return BookingListSerializer
        elif self.action == 'create':
            return BookingCreateSerializer
        return BookingDetailSerializer
    
    @extend_schema(
        summary="List my bookings",
        description="Get bookings for the authenticated user (guest perspective)."
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @extend_schema(
        summary="Create booking",
        description="Create a new booking for a property."
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    
    @extend_schema(
        summary="Get host bookings",
        description="Get bookings for properties owned by the authenticated host."
    )
    @action(detail=False, methods=['get'])
    def host_bookings(self, request):
        """Get bookings for properties owned by the authenticated host."""
        if not request.user.is_host:
            return Response(
                {'error': 'Only hosts can access this endpoint.'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = BookingListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = BookingListSerializer(queryset, many=True)
        return Response(serializer.data)
    
    @extend_schema(
        summary="Confirm booking",
        description="Confirm a pending booking (host only)."
    )
    @action(detail=True, methods=['post'])
    def confirm(self, request, pk=None):
        """Confirm a booking (host only)."""
        booking = self.get_object()
        
        # Check if user is the host
        if booking.property.host != request.user:
            return Response(
                {'error': 'Only the property host can confirm bookings.'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if booking.status != Booking.Status.PENDING:
            return Response(
                {'error': 'Only pending bookings can be confirmed.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        booking.status = Booking.Status.CONFIRMED
        booking.confirmed_at = timezone.now()
        booking.save()
        
        return Response({'message': 'Booking confirmed successfully.'})
    
    @extend_schema(
        summary="Cancel booking",
        description="Cancel a booking with refund calculation."
    )
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a booking."""
        booking = self.get_object()
        
        # Check permissions
        if not (booking.guest == request.user or booking.property.host == request.user):
            return Response(
                {'error': 'You can only cancel your own bookings or bookings for your properties.'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if not booking.can_be_cancelled():
            return Response(
                {'error': 'This booking cannot be cancelled.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = BookingCancellationSerializer(
            data=request.data,
            context={'booking': booking, 'request': request}
        )
        
        if serializer.is_valid():
            cancellation = serializer.save()
            return Response({
                'message': 'Booking cancelled successfully.',
                'refund_amount': cancellation.refund_amount
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @extend_schema(
        summary="Get booking messages",
        description="Get messages for a specific booking."
    )
    @action(detail=True, methods=['get'])
    def messages(self, request, pk=None):
        """Get messages for a booking."""
        booking = self.get_object()
        messages = booking.messages.all().order_by('created_at')
        serializer = BookingMessageSerializer(messages, many=True)
        return Response(serializer.data)
    
    @extend_schema(
        summary="Send booking message",
        description="Send a message related to a booking."
    )
    @action(detail=True, methods=['post'])
    def send_message(self, request, pk=None):
        """Send a message for a booking."""
        booking = self.get_object()
        
        serializer = BookingMessageSerializer(
            data=request.data,
            context={'booking': booking, 'request': request}
        )
        
        if serializer.is_valid():
            message = serializer.save()
            return Response(
                BookingMessageSerializer(message).data,
                status=status.HTTP_201_CREATED
            )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @extend_schema(
        summary="Mark messages as read",
        description="Mark all messages in a booking as read."
    )
    @action(detail=True, methods=['post'])
    def mark_messages_read(self, request, pk=None):
        """Mark all messages in a booking as read."""
        booking = self.get_object()
        
        # Mark messages as read for the current user
        booking.messages.exclude(sender=request.user).update(is_read=True)
        
        return Response({'message': 'Messages marked as read.'})
    
    def perform_create(self, serializer):
        """Set the guest when creating a booking."""
        serializer.save(guest=self.request.user)
