# Generated by Django 5.2 on 2025-06-04 01:44

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("properties", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Booking",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("check_in_date", models.DateField(verbose_name="check-in date")),
                ("check_out_date", models.DateField(verbose_name="check-out date")),
                (
                    "number_of_guests",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="number of guests",
                    ),
                ),
                (
                    "nightly_rate",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="nightly rate",
                    ),
                ),
                (
                    "number_of_nights",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="number of nights",
                    ),
                ),
                (
                    "subtotal",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="subtotal",
                    ),
                ),
                (
                    "cleaning_fee",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=8,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="cleaning fee",
                    ),
                ),
                (
                    "service_fee",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=8,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="service fee",
                    ),
                ),
                (
                    "taxes",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=8,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="taxes",
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="total amount",
                    ),
                ),
                (
                    "stripe_payment_intent_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Stripe Payment Intent ID",
                    ),
                ),
                (
                    "stripe_charge_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Stripe Charge ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("confirmed", "Confirmed"),
                            ("cancelled", "Cancelled"),
                            ("completed", "Completed"),
                            ("refunded", "Refunded"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="status",
                    ),
                ),
                (
                    "special_requests",
                    models.TextField(
                        blank=True,
                        help_text="Guest special requests or notes",
                        verbose_name="special requests",
                    ),
                ),
                (
                    "host_notes",
                    models.TextField(
                        blank=True,
                        help_text="Internal notes from the host",
                        verbose_name="host notes",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("confirmed_at", models.DateTimeField(blank=True, null=True)),
                ("cancelled_at", models.DateTimeField(blank=True, null=True)),
                (
                    "guest",
                    models.ForeignKey(
                        limit_choices_to={"role": "guest"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookings",
                        to="properties.property",
                    ),
                ),
            ],
            options={
                "verbose_name": "Booking",
                "verbose_name_plural": "Bookings",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="BookingCancellation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cancelled_by",
                    models.CharField(
                        choices=[
                            ("guest", "Guest"),
                            ("host", "Host"),
                            ("admin", "Admin"),
                            ("system", "System"),
                        ],
                        max_length=10,
                        verbose_name="cancelled by",
                    ),
                ),
                ("reason", models.TextField(verbose_name="cancellation reason")),
                (
                    "refund_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="refund amount",
                    ),
                ),
                (
                    "refund_processed",
                    models.BooleanField(default=False, verbose_name="refund processed"),
                ),
                (
                    "stripe_refund_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Stripe Refund ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "booking",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="cancellation",
                        to="bookings.booking",
                    ),
                ),
            ],
            options={
                "verbose_name": "Booking Cancellation",
                "verbose_name_plural": "Booking Cancellations",
            },
        ),
        migrations.CreateModel(
            name="BookingMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("message", models.TextField(verbose_name="message")),
                ("is_read", models.BooleanField(default=False, verbose_name="is read")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="bookings.booking",
                    ),
                ),
                (
                    "sender",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_booking_messages",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Booking Message",
                "verbose_name_plural": "Booking Messages",
                "ordering": ["created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(fields=["guest"], name="bookings_bo_guest_i_05b863_idx"),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(
                fields=["property"], name="bookings_bo_propert_749eda_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(fields=["status"], name="bookings_bo_status_233e96_idx"),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(
                fields=["check_in_date", "check_out_date"],
                name="bookings_bo_check_i_64cf26_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(
                fields=["created_at"], name="bookings_bo_created_1720a2_idx"
            ),
        ),
        migrations.AddConstraint(
            model_name="booking",
            constraint=models.UniqueConstraint(
                condition=models.Q(("status__in", ["confirmed", "pending"])),
                fields=("property",),
                name="no_overlapping_bookings",
                violation_error_message="Overlapping bookings are not allowed.",
            ),
        ),
        migrations.AddIndex(
            model_name="bookingcancellation",
            index=models.Index(
                fields=["cancelled_by"], name="bookings_bo_cancell_fdd4bb_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bookingcancellation",
            index=models.Index(
                fields=["created_at"], name="bookings_bo_created_3fff9a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bookingmessage",
            index=models.Index(
                fields=["booking", "created_at"], name="bookings_bo_booking_fbd761_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bookingmessage",
            index=models.Index(
                fields=["sender"], name="bookings_bo_sender__e8467b_idx"
            ),
        ),
    ]
