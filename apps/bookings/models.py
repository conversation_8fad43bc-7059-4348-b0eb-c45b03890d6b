"""
Booking models for HomeSwap Platform.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from django.core.exceptions import ValidationError
from decimal import Decimal
import uuid

User = get_user_model()


class Booking(models.Model):
    """
    Booking model with Stripe payment integration and overlap prevention.
    """
    
    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        CONFIRMED = 'confirmed', _('Confirmed')
        CANCELLED = 'cancelled', _('Cancelled')
        COMPLETED = 'completed', _('Completed')
        REFUNDED = 'refunded', _('Refunded')
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    guest = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='bookings',
        limit_choices_to={'role': 'guest'}
    )
    property = models.ForeignKey(
        'properties.Property',
        on_delete=models.CASCADE,
        related_name='bookings'
    )
    
    # Booking dates
    check_in_date = models.DateField(_('check-in date'))
    check_out_date = models.DateField(_('check-out date'))
    
    # Guest information
    number_of_guests = models.PositiveIntegerField(
        _('number of guests'),
        validators=[MinValueValidator(1)]
    )
    
    # Pricing breakdown
    nightly_rate = models.DecimalField(
        _('nightly rate'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    number_of_nights = models.PositiveIntegerField(
        _('number of nights'),
        validators=[MinValueValidator(1)]
    )
    subtotal = models.DecimalField(
        _('subtotal'),
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    cleaning_fee = models.DecimalField(
        _('cleaning fee'),
        max_digits=8,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)]
    )
    service_fee = models.DecimalField(
        _('service fee'),
        max_digits=8,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)]
    )
    taxes = models.DecimalField(
        _('taxes'),
        max_digits=8,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)]
    )
    total_amount = models.DecimalField(
        _('total amount'),
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    
    # Payment information
    stripe_payment_intent_id = models.CharField(
        _('Stripe Payment Intent ID'),
        max_length=255,
        blank=True,
        null=True
    )
    stripe_charge_id = models.CharField(
        _('Stripe Charge ID'),
        max_length=255,
        blank=True,
        null=True
    )
    
    # Booking status and metadata
    status = models.CharField(
        _('status'),
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    special_requests = models.TextField(
        _('special requests'),
        blank=True,
        help_text=_('Guest special requests or notes')
    )
    host_notes = models.TextField(
        _('host notes'),
        blank=True,
        help_text=_('Internal notes from the host')
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    confirmed_at = models.DateTimeField(blank=True, null=True)
    cancelled_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        verbose_name = _('Booking')
        verbose_name_plural = _('Bookings')
        constraints = [
            # Prevent overlapping bookings for the same property
            models.UniqueConstraint(
                fields=['property'],
                condition=models.Q(status__in=['confirmed', 'pending']),
                name='no_overlapping_bookings',
                violation_error_message=_('Overlapping bookings are not allowed.')
            ),
        ]
        indexes = [
            models.Index(fields=['guest']),
            models.Index(fields=['property']),
            models.Index(fields=['status']),
            models.Index(fields=['check_in_date', 'check_out_date']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Booking {self.id} - {self.property.title} ({self.check_in_date} to {self.check_out_date})"
    
    def clean(self):
        """Validate booking dates and availability."""
        if self.check_in_date and self.check_out_date:
            if self.check_in_date >= self.check_out_date:
                raise ValidationError(_('Check-out date must be after check-in date.'))
            
            if self.number_of_guests > self.property.max_guests:
                raise ValidationError(
                    _('Number of guests exceeds property maximum of {max_guests}.').format(
                        max_guests=self.property.max_guests
                    )
                )
            
            # Check for overlapping bookings
            overlapping_bookings = Booking.objects.filter(
                property=self.property,
                status__in=[self.Status.CONFIRMED, self.Status.PENDING],
                check_in_date__lt=self.check_out_date,
                check_out_date__gt=self.check_in_date
            ).exclude(pk=self.pk)
            
            if overlapping_bookings.exists():
                raise ValidationError(_('Property is not available for the selected dates.'))
    
    def save(self, *args, **kwargs):
        # Calculate derived fields
        if self.check_in_date and self.check_out_date:
            self.number_of_nights = (self.check_out_date - self.check_in_date).days
        
        if self.nightly_rate and self.number_of_nights:
            self.subtotal = self.nightly_rate * self.number_of_nights
        
        if hasattr(self, 'subtotal'):
            self.total_amount = (
                self.subtotal + 
                self.cleaning_fee + 
                self.service_fee + 
                self.taxes
            )
        
        self.full_clean()
        super().save(*args, **kwargs)
    
    def is_active(self):
        """Check if booking is active (confirmed and not completed/cancelled)."""
        return self.status == self.Status.CONFIRMED

    def can_be_cancelled(self):
        """Check if booking can be cancelled."""
        return self.status in [self.Status.PENDING, self.Status.CONFIRMED]

    def get_host(self):
        """Get the host of the booked property."""
        return self.property.host


class BookingMessage(models.Model):
    """
    Messages between guest and host for a specific booking.
    """
    booking = models.ForeignKey(
        Booking,
        on_delete=models.CASCADE,
        related_name='messages'
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_booking_messages'
    )
    message = models.TextField(_('message'))
    is_read = models.BooleanField(_('is read'), default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = _('Booking Message')
        verbose_name_plural = _('Booking Messages')
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['booking', 'created_at']),
            models.Index(fields=['sender']),
        ]
    
    def __str__(self):
        return f"Message from {self.sender.get_full_name()} - {self.created_at}"


class BookingCancellation(models.Model):
    """
    Track booking cancellations with reasons and refund information.
    """
    
    class CancelledBy(models.TextChoices):
        GUEST = 'guest', _('Guest')
        HOST = 'host', _('Host')
        ADMIN = 'admin', _('Admin')
        SYSTEM = 'system', _('System')
    
    booking = models.OneToOneField(
        Booking,
        on_delete=models.CASCADE,
        related_name='cancellation'
    )
    cancelled_by = models.CharField(
        _('cancelled by'),
        max_length=10,
        choices=CancelledBy.choices
    )
    reason = models.TextField(_('cancellation reason'))
    refund_amount = models.DecimalField(
        _('refund amount'),
        max_digits=12,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)]
    )
    refund_processed = models.BooleanField(
        _('refund processed'),
        default=False
    )
    stripe_refund_id = models.CharField(
        _('Stripe Refund ID'),
        max_length=255,
        blank=True,
        null=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = _('Booking Cancellation')
        verbose_name_plural = _('Booking Cancellations')
        indexes = [
            models.Index(fields=['cancelled_by']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Cancellation for {self.booking.id} by {self.cancelled_by}"
