"""
Serializers for the bookings app.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal

from .models import Booking, BookingMessage, BookingCancellation
from properties.serializers import PropertyListSerializer

User = get_user_model()


class BookingListSerializer(serializers.ModelSerializer):
    """
    Serializer for booking list view (minimal data).
    """
    property_title = serializers.CharField(source='property.title', read_only=True)
    property_city = serializers.CharField(source='property.city', read_only=True)
    guest_name = serializers.CharField(source='guest.get_full_name', read_only=True)
    host_name = serializers.CharField(source='property.host.get_full_name', read_only=True)
    
    class Meta:
        model = Booking
        fields = [
            'id', 'property_title', 'property_city', 'guest_name', 'host_name',
            'check_in_date', 'check_out_date', 'number_of_guests',
            'total_amount', 'status', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class BookingDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for booking detail view (full data).
    """
    property = PropertyListSerializer(read_only=True)
    guest_name = serializers.CharField(source='guest.get_full_name', read_only=True)
    guest_email = serializers.CharField(source='guest.email', read_only=True)
    host_name = serializers.CharField(source='property.host.get_full_name', read_only=True)
    can_cancel = serializers.SerializerMethodField()
    is_active_booking = serializers.SerializerMethodField()
    
    class Meta:
        model = Booking
        fields = [
            'id', 'property', 'guest_name', 'guest_email', 'host_name',
            'check_in_date', 'check_out_date', 'number_of_guests',
            'nightly_rate', 'number_of_nights', 'subtotal',
            'cleaning_fee', 'service_fee', 'taxes', 'total_amount',
            'stripe_payment_intent_id', 'status', 'special_requests',
            'host_notes', 'can_cancel', 'is_active_booking',
            'created_at', 'updated_at', 'confirmed_at'
        ]
        read_only_fields = [
            'id', 'number_of_nights', 'subtotal', 'total_amount',
            'created_at', 'updated_at', 'confirmed_at'
        ]
    
    def get_can_cancel(self, obj):
        """Check if booking can be cancelled."""
        return obj.can_be_cancelled()
    
    def get_is_active_booking(self, obj):
        """Check if booking is active."""
        return obj.is_active()


class BookingCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating bookings.
    """
    property_id = serializers.UUIDField(write_only=True)
    
    class Meta:
        model = Booking
        fields = [
            'property_id', 'check_in_date', 'check_out_date',
            'number_of_guests', 'special_requests'
        ]
    
    def validate(self, attrs):
        """Validate booking data."""
        from properties.models import Property
        
        # Get property
        try:
            property_obj = Property.objects.get(id=attrs['property_id'])
        except Property.DoesNotExist:
            raise serializers.ValidationError("Property not found.")
        
        # Validate dates
        check_in = attrs['check_in_date']
        check_out = attrs['check_out_date']
        
        if check_in >= check_out:
            raise serializers.ValidationError("Check-out date must be after check-in date.")
        
        if check_in < timezone.now().date():
            raise serializers.ValidationError("Check-in date cannot be in the past.")
        
        # Validate guest capacity
        if attrs['number_of_guests'] > property_obj.max_guests:
            raise serializers.ValidationError(
                f"Number of guests exceeds property maximum of {property_obj.max_guests}."
            )
        
        # Check minimum stay
        nights = (check_out - check_in).days
        if nights < property_obj.minimum_stay:
            raise serializers.ValidationError(
                f"Minimum stay is {property_obj.minimum_stay} nights."
            )
        
        # Check maximum stay
        if nights > property_obj.maximum_stay:
            raise serializers.ValidationError(
                f"Maximum stay is {property_obj.maximum_stay} nights."
            )
        
        # Check for overlapping bookings
        overlapping = Booking.objects.filter(
            property=property_obj,
            status__in=[Booking.Status.CONFIRMED, Booking.Status.PENDING],
            check_in_date__lt=check_out,
            check_out_date__gt=check_in
        ).exists()
        
        if overlapping:
            raise serializers.ValidationError("Property is not available for the selected dates.")
        
        attrs['property'] = property_obj
        return attrs
    
    def create(self, validated_data):
        """Create booking with calculated pricing."""
        property_obj = validated_data.pop('property')
        validated_data.pop('property_id')
        
        # Calculate pricing
        check_in = validated_data['check_in_date']
        check_out = validated_data['check_out_date']
        nights = (check_out - check_in).days
        
        # Get nightly rate (could be dynamic based on availability)
        nightly_rate = property_obj.daily_price
        subtotal = nightly_rate * nights
        
        # Calculate fees
        cleaning_fee = property_obj.cleaning_fee
        service_fee = subtotal * Decimal('0.03')  # 3% service fee
        taxes = (subtotal + service_fee) * Decimal('0.08')  # 8% tax
        
        total_amount = subtotal + cleaning_fee + service_fee + taxes
        
        # Create booking
        booking = Booking.objects.create(
            guest=self.context['request'].user,
            property=property_obj,
            nightly_rate=nightly_rate,
            number_of_nights=nights,
            subtotal=subtotal,
            cleaning_fee=cleaning_fee,
            service_fee=service_fee,
            taxes=taxes,
            total_amount=total_amount,
            **validated_data
        )
        
        return booking


class BookingMessageSerializer(serializers.ModelSerializer):
    """
    Serializer for booking messages.
    """
    sender_name = serializers.CharField(source='sender.get_full_name', read_only=True)
    
    class Meta:
        model = BookingMessage
        fields = [
            'id', 'sender', 'sender_name', 'message',
            'is_read', 'created_at'
        ]
        read_only_fields = ['id', 'sender', 'created_at']
    
    def create(self, validated_data):
        """Create message with sender from request."""
        validated_data['sender'] = self.context['request'].user
        validated_data['booking'] = self.context['booking']
        return super().create(validated_data)


class BookingCancellationSerializer(serializers.ModelSerializer):
    """
    Serializer for booking cancellations.
    """
    class Meta:
        model = BookingCancellation
        fields = [
            'reason', 'refund_amount', 'refund_processed'
        ]
        read_only_fields = ['refund_processed']
    
    def create(self, validated_data):
        """Create cancellation record."""
        booking = self.context['booking']
        user = self.context['request'].user
        
        # Determine who cancelled
        if user == booking.guest:
            cancelled_by = BookingCancellation.CancelledBy.GUEST
        elif user == booking.get_host():
            cancelled_by = BookingCancellation.CancelledBy.HOST
        else:
            cancelled_by = BookingCancellation.CancelledBy.ADMIN
        
        # Calculate refund amount (simplified logic)
        days_until_checkin = (booking.check_in_date - timezone.now().date()).days
        if days_until_checkin >= 7:
            refund_percentage = Decimal('0.95')  # 95% refund
        elif days_until_checkin >= 3:
            refund_percentage = Decimal('0.50')  # 50% refund
        else:
            refund_percentage = Decimal('0.00')  # No refund
        
        refund_amount = booking.total_amount * refund_percentage
        
        cancellation = BookingCancellation.objects.create(
            booking=booking,
            cancelled_by=cancelled_by,
            refund_amount=refund_amount,
            **validated_data
        )
        
        # Update booking status
        booking.status = Booking.Status.CANCELLED
        booking.cancelled_at = timezone.now()
        booking.save()
        
        return cancellation
