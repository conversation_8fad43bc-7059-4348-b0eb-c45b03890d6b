"""
Payment services for Stripe integration.
"""

import stripe
from django.conf import settings
from django.utils import timezone
from decimal import Decimal
from .models import Payment, Refund, StripeWebhookEvent

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class StripePaymentService:
    """
    Service class for handling Stripe payments.
    """
    
    @staticmethod
    def create_payment_intent(booking, user):
        """
        Create a Stripe Payment Intent for a booking.
        """
        try:
            # Convert amount to cents (Stripe expects cents)
            amount_cents = int(booking.total_amount * 100)
            
            # Create Payment Intent
            intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency='usd',
                metadata={
                    'booking_id': str(booking.id),
                    'user_id': str(user.id),
                    'property_id': str(booking.property.id),
                },
                description=f"Booking for {booking.property.title}",
                receipt_email=user.email,
            )
            
            # Create Payment record
            payment = Payment.objects.create(
                booking=booking,
                payer=user,
                amount=booking.total_amount,
                stripe_payment_intent_id=intent.id,
                status=Payment.Status.PENDING,
                platform_fee=booking.total_amount * Decimal('0.03'),  # 3% platform fee
                host_payout=booking.total_amount * Decimal('0.97'),   # 97% to host
            )
            
            return {
                'client_secret': intent.client_secret,
                'payment_intent_id': intent.id,
                'payment_id': payment.id,
            }
            
        except stripe.error.StripeError as e:
            raise Exception(f"Stripe error: {str(e)}")
    
    @staticmethod
    def confirm_payment(payment_intent_id):
        """
        Confirm a payment and update booking status.
        """
        try:
            # Retrieve Payment Intent from Stripe
            intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            
            # Get payment record
            payment = Payment.objects.get(stripe_payment_intent_id=payment_intent_id)
            
            if intent.status == 'succeeded':
                # Update payment status
                payment.status = Payment.Status.SUCCEEDED
                payment.stripe_charge_id = intent.latest_charge
                payment.processed_at = timezone.now()
                payment.save()
                
                # Update booking status
                booking = payment.booking
                booking.status = booking.Status.CONFIRMED
                booking.confirmed_at = timezone.now()
                booking.stripe_payment_intent_id = payment_intent_id
                booking.save()
                
                return True
            else:
                # Payment failed
                payment.status = Payment.Status.FAILED
                payment.failure_reason = f"Payment Intent status: {intent.status}"
                payment.save()
                
                return False
                
        except (stripe.error.StripeError, Payment.DoesNotExist) as e:
            raise Exception(f"Error confirming payment: {str(e)}")
    
    @staticmethod
    def create_refund(payment, amount, reason, initiated_by):
        """
        Create a refund for a payment.
        """
        try:
            # Create refund in Stripe
            stripe_refund = stripe.Refund.create(
                payment_intent=payment.stripe_payment_intent_id,
                amount=int(amount * 100),  # Convert to cents
                reason=reason.lower().replace('_', ' '),
                metadata={
                    'payment_id': str(payment.id),
                    'booking_id': str(payment.booking.id),
                    'initiated_by': str(initiated_by.id),
                }
            )
            
            # Create Refund record
            refund = Refund.objects.create(
                payment=payment,
                amount=amount,
                reason=reason,
                stripe_refund_id=stripe_refund.id,
                initiated_by=initiated_by,
                status=Refund.Status.PROCESSING,
            )
            
            return refund
            
        except stripe.error.StripeError as e:
            raise Exception(f"Stripe refund error: {str(e)}")
    
    @staticmethod
    def handle_webhook_event(event_data):
        """
        Handle Stripe webhook events.
        """
        event_id = event_data.get('id')
        event_type = event_data.get('type')
        
        # Check if we've already processed this event
        if StripeWebhookEvent.objects.filter(stripe_event_id=event_id).exists():
            return {'status': 'already_processed'}
        
        # Create webhook event record
        webhook_event = StripeWebhookEvent.objects.create(
            stripe_event_id=event_id,
            event_type=event_type,
            data=event_data,
        )
        
        try:
            if event_type == 'payment_intent.succeeded':
                payment_intent = event_data['data']['object']
                StripePaymentService.confirm_payment(payment_intent['id'])
                
            elif event_type == 'payment_intent.payment_failed':
                payment_intent = event_data['data']['object']
                try:
                    payment = Payment.objects.get(
                        stripe_payment_intent_id=payment_intent['id']
                    )
                    payment.status = Payment.Status.FAILED
                    payment.failure_reason = payment_intent.get('last_payment_error', {}).get('message', 'Unknown error')
                    payment.save()
                except Payment.DoesNotExist:
                    pass
                    
            elif event_type == 'charge.dispute.created':
                # Handle dispute creation
                charge = event_data['data']['object']
                # Add dispute handling logic here
                pass
            
            # Mark event as processed
            webhook_event.processed = True
            webhook_event.processed_at = timezone.now()
            webhook_event.save()
            
            return {'status': 'processed'}
            
        except Exception as e:
            # Log error but don't raise to avoid webhook retry
            webhook_event.data['error'] = str(e)
            webhook_event.save()
            return {'status': 'error', 'message': str(e)}


class PayoutService:
    """
    Service for handling host payouts.
    """
    
    @staticmethod
    def schedule_payout(booking, payout_date):
        """
        Schedule a payout for a completed booking.
        """
        from .models import Payout
        
        # Calculate payout amount (total - platform fee)
        platform_fee = booking.total_amount * Decimal('0.03')
        payout_amount = booking.total_amount - platform_fee
        
        payout = Payout.objects.create(
            host=booking.property.host,
            booking=booking,
            amount=payout_amount,
            scheduled_payout_date=payout_date,
            status=Payout.Status.PENDING,
        )
        
        return payout
    
    @staticmethod
    def process_payout(payout):
        """
        Process a scheduled payout via Stripe.
        """
        try:
            # In a real implementation, you would:
            # 1. Create a Stripe Connect account for the host
            # 2. Transfer funds to their account
            # For now, we'll just mark as paid
            
            payout.status = Payout.Status.PAID
            payout.processed_at = timezone.now()
            payout.save()
            
            return True
            
        except Exception as e:
            payout.status = Payout.Status.FAILED
            payout.failure_reason = str(e)
            payout.save()
            return False
