"""
Views for the payments app.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.views.decorators.csrf import csrf_exempt
from django.http import HttpResponse
from django.conf import settings
from drf_spectacular.utils import extend_schema
import stripe
import json

from .models import Payment, Refund, Payout
from .serializers import (
    PaymentSerializer,
    CreatePaymentIntentSerializer,
    RefundSerializer,
    PayoutSerializer
)
from .services import StripePaymentService, PayoutService
from bookings.models import Booking


class PaymentViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing payments.
    """
    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter payments based on user."""
        user = self.request.user
        if user.is_host:
            # Hosts can see payments for their properties
            return Payment.objects.filter(
                booking__property__host=user
            ).select_related('booking', 'payer')
        else:
            # Guests can see their own payments
            return Payment.objects.filter(
                payer=user
            ).select_related('booking')
    
    @extend_schema(
        summary="Create payment intent",
        description="Create a Stripe Payment Intent for a booking."
    )
    @action(detail=False, methods=['post'])
    def create_payment_intent(self, request):
        """Create a Stripe Payment Intent for a booking."""
        serializer = CreatePaymentIntentSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            booking_id = serializer.validated_data['booking_id']
            
            try:
                booking = Booking.objects.get(id=booking_id)
                result = StripePaymentService.create_payment_intent(booking, request.user)
                
                return Response(result, status=status.HTTP_201_CREATED)
                
            except Booking.DoesNotExist:
                return Response(
                    {'error': 'Booking not found.'},
                    status=status.HTTP_404_NOT_FOUND
                )
            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @extend_schema(
        summary="Confirm payment",
        description="Confirm a payment using Payment Intent ID."
    )
    @action(detail=False, methods=['post'])
    def confirm_payment(self, request):
        """Confirm a payment."""
        payment_intent_id = request.data.get('payment_intent_id')
        
        if not payment_intent_id:
            return Response(
                {'error': 'payment_intent_id is required.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            success = StripePaymentService.confirm_payment(payment_intent_id)
            
            if success:
                return Response({'message': 'Payment confirmed successfully.'})
            else:
                return Response(
                    {'error': 'Payment confirmation failed.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class RefundViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing refunds.
    """
    serializer_class = RefundSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter refunds based on user."""
        user = self.request.user
        if user.is_host:
            return Refund.objects.filter(
                payment__booking__property__host=user
            ).select_related('payment', 'initiated_by')
        else:
            return Refund.objects.filter(
                payment__payer=user
            ).select_related('payment', 'initiated_by')


class PayoutViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing payouts (hosts only).
    """
    serializer_class = PayoutSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Only hosts can see their payouts."""
        if not self.request.user.is_host:
            return Payout.objects.none()
        
        return Payout.objects.filter(
            host=self.request.user
        ).select_related('booking')


@csrf_exempt
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def stripe_webhook(request):
    """
    Handle Stripe webhook events.
    """
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    endpoint_secret = settings.STRIPE_WEBHOOK_SECRET
    
    try:
        # Verify webhook signature
        event = stripe.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
    except ValueError:
        # Invalid payload
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError:
        # Invalid signature
        return HttpResponse(status=400)
    
    # Handle the event
    try:
        result = StripePaymentService.handle_webhook_event(event)
        return HttpResponse(json.dumps(result), content_type='application/json')
    except Exception as e:
        return HttpResponse(
            json.dumps({'error': str(e)}),
            content_type='application/json',
            status=500
        )
