"""
Payment models for HomeSwap Platform with Stripe integration.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
import uuid

User = get_user_model()


class Payment(models.Model):
    """
    Payment model to track all payment transactions.
    """
    
    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        PROCESSING = 'processing', _('Processing')
        SUCCEEDED = 'succeeded', _('Succeeded')
        FAILED = 'failed', _('Failed')
        CANCELLED = 'cancelled', _('Cancelled')
        REFUNDED = 'refunded', _('Refunded')
        PARTIALLY_REFUNDED = 'partially_refunded', _('Partially Refunded')
    
    class PaymentType(models.TextChoices):
        BOOKING = 'booking', _('Booking Payment')
        SECURITY_DEPOSIT = 'security_deposit', _('Security Deposit')
        ADDITIONAL_CHARGES = 'additional_charges', _('Additional Charges')
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    booking = models.ForeignKey(
        'bookings.Booking',
        on_delete=models.CASCADE,
        related_name='payments'
    )
    payer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='payments_made'
    )
    
    # Payment details
    payment_type = models.CharField(
        _('payment type'),
        max_length=20,
        choices=PaymentType.choices,
        default=PaymentType.BOOKING
    )
    amount = models.DecimalField(
        _('amount'),
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    currency = models.CharField(
        _('currency'),
        max_length=3,
        default='USD'
    )
    
    # Stripe integration
    stripe_payment_intent_id = models.CharField(
        _('Stripe Payment Intent ID'),
        max_length=255,
        unique=True,
        blank=True,
        null=True
    )
    stripe_charge_id = models.CharField(
        _('Stripe Charge ID'),
        max_length=255,
        blank=True,
        null=True
    )
    stripe_customer_id = models.CharField(
        _('Stripe Customer ID'),
        max_length=255,
        blank=True,
        null=True
    )
    
    # Payment status and metadata
    status = models.CharField(
        _('status'),
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    failure_reason = models.TextField(
        _('failure reason'),
        blank=True,
        help_text=_('Reason for payment failure')
    )
    
    # Platform fees
    platform_fee = models.DecimalField(
        _('platform fee'),
        max_digits=8,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)]
    )
    host_payout = models.DecimalField(
        _('host payout'),
        max_digits=12,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)]
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    processed_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')
        indexes = [
            models.Index(fields=['booking']),
            models.Index(fields=['payer']),
            models.Index(fields=['status']),
            models.Index(fields=['stripe_payment_intent_id']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Payment {self.id} - {self.amount} {self.currency} ({self.status})"
    
    @property
    def host(self):
        """Get the host who will receive the payout."""
        return self.booking.property.host


class Refund(models.Model):
    """
    Refund model to track refund transactions.
    """
    
    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        PROCESSING = 'processing', _('Processing')
        SUCCEEDED = 'succeeded', _('Succeeded')
        FAILED = 'failed', _('Failed')
        CANCELLED = 'cancelled', _('Cancelled')
    
    class RefundReason(models.TextChoices):
        GUEST_CANCELLATION = 'guest_cancellation', _('Guest Cancellation')
        HOST_CANCELLATION = 'host_cancellation', _('Host Cancellation')
        DISPUTE = 'dispute', _('Dispute')
        DUPLICATE = 'duplicate', _('Duplicate Payment')
        FRAUDULENT = 'fraudulent', _('Fraudulent')
        OTHER = 'other', _('Other')
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='refunds'
    )
    
    # Refund details
    amount = models.DecimalField(
        _('refund amount'),
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    reason = models.CharField(
        _('refund reason'),
        max_length=20,
        choices=RefundReason.choices
    )
    description = models.TextField(
        _('refund description'),
        blank=True
    )
    
    # Stripe integration
    stripe_refund_id = models.CharField(
        _('Stripe Refund ID'),
        max_length=255,
        unique=True,
        blank=True,
        null=True
    )
    
    # Refund status
    status = models.CharField(
        _('status'),
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    failure_reason = models.TextField(
        _('failure reason'),
        blank=True
    )
    
    # Metadata
    initiated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='initiated_refunds'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    processed_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        verbose_name = _('Refund')
        verbose_name_plural = _('Refunds')
        indexes = [
            models.Index(fields=['payment']),
            models.Index(fields=['status']),
            models.Index(fields=['stripe_refund_id']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Refund {self.id} - {self.amount} ({self.status})"


class Payout(models.Model):
    """
    Payout model to track payments to hosts.
    """
    
    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        PROCESSING = 'processing', _('Processing')
        PAID = 'paid', _('Paid')
        FAILED = 'failed', _('Failed')
        CANCELLED = 'cancelled', _('Cancelled')
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    host = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='payouts',
        limit_choices_to={'role': 'host'}
    )
    booking = models.ForeignKey(
        'bookings.Booking',
        on_delete=models.CASCADE,
        related_name='payouts'
    )
    
    # Payout details
    amount = models.DecimalField(
        _('payout amount'),
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    currency = models.CharField(
        _('currency'),
        max_length=3,
        default='USD'
    )
    
    # Stripe integration
    stripe_transfer_id = models.CharField(
        _('Stripe Transfer ID'),
        max_length=255,
        unique=True,
        blank=True,
        null=True
    )
    stripe_account_id = models.CharField(
        _('Stripe Connected Account ID'),
        max_length=255,
        blank=True,
        null=True
    )
    
    # Payout status
    status = models.CharField(
        _('status'),
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    failure_reason = models.TextField(
        _('failure reason'),
        blank=True
    )
    
    # Timing
    scheduled_payout_date = models.DateField(
        _('scheduled payout date'),
        help_text=_('Date when payout is scheduled to be processed')
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    processed_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        verbose_name = _('Payout')
        verbose_name_plural = _('Payouts')
        indexes = [
            models.Index(fields=['host']),
            models.Index(fields=['booking']),
            models.Index(fields=['status']),
            models.Index(fields=['scheduled_payout_date']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Payout {self.id} - {self.amount} {self.currency} to {self.host.get_full_name()}"


class StripeWebhookEvent(models.Model):
    """
    Track Stripe webhook events for idempotency and debugging.
    """
    stripe_event_id = models.CharField(
        _('Stripe Event ID'),
        max_length=255,
        unique=True
    )
    event_type = models.CharField(
        _('event type'),
        max_length=100
    )
    processed = models.BooleanField(
        _('processed'),
        default=False
    )
    data = models.JSONField(
        _('event data'),
        help_text=_('Raw webhook event data from Stripe')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        verbose_name = _('Stripe Webhook Event')
        verbose_name_plural = _('Stripe Webhook Events')
        indexes = [
            models.Index(fields=['stripe_event_id']),
            models.Index(fields=['event_type']),
            models.Index(fields=['processed']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Webhook {self.stripe_event_id} - {self.event_type}"
