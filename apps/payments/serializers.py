"""
Serializers for the payments app.
"""

from rest_framework import serializers
from .models import Payment, Refund, Payout


class PaymentSerializer(serializers.ModelSerializer):
    """
    Serializer for Payment model.
    """
    booking_id = serializers.UUIDField(source='booking.id', read_only=True)
    payer_name = serializers.CharField(source='payer.get_full_name', read_only=True)
    
    class Meta:
        model = Payment
        fields = [
            'id', 'booking_id', 'payer_name', 'payment_type',
            'amount', 'currency', 'status', 'platform_fee',
            'host_payout', 'stripe_payment_intent_id',
            'created_at', 'processed_at'
        ]
        read_only_fields = [
            'id', 'status', 'stripe_payment_intent_id',
            'created_at', 'processed_at'
        ]


class CreatePaymentIntentSerializer(serializers.Serializer):
    """
    Serializer for creating Stripe Payment Intent.
    """
    booking_id = serializers.UUIDField()
    
    def validate_booking_id(self, value):
        """Validate that booking exists and belongs to user."""
        from bookings.models import Booking
        
        try:
            booking = Booking.objects.get(id=value)
        except Booking.DoesNotExist:
            raise serializers.ValidationError("Booking not found.")
        
        # Check if user is the guest
        if booking.guest != self.context['request'].user:
            raise serializers.ValidationError("You can only pay for your own bookings.")
        
        # Check if booking is in correct status
        if booking.status != Booking.Status.PENDING:
            raise serializers.ValidationError("Payment can only be made for pending bookings.")
        
        return value


class RefundSerializer(serializers.ModelSerializer):
    """
    Serializer for Refund model.
    """
    payment_id = serializers.UUIDField(source='payment.id', read_only=True)
    initiated_by_name = serializers.CharField(source='initiated_by.get_full_name', read_only=True)
    
    class Meta:
        model = Refund
        fields = [
            'id', 'payment_id', 'amount', 'reason', 'description',
            'status', 'initiated_by_name', 'stripe_refund_id',
            'created_at', 'processed_at'
        ]
        read_only_fields = [
            'id', 'status', 'stripe_refund_id',
            'created_at', 'processed_at'
        ]


class PayoutSerializer(serializers.ModelSerializer):
    """
    Serializer for Payout model.
    """
    host_name = serializers.CharField(source='host.get_full_name', read_only=True)
    booking_id = serializers.UUIDField(source='booking.id', read_only=True)
    
    class Meta:
        model = Payout
        fields = [
            'id', 'host_name', 'booking_id', 'amount', 'currency',
            'status', 'scheduled_payout_date', 'stripe_transfer_id',
            'created_at', 'processed_at'
        ]
        read_only_fields = [
            'id', 'status', 'stripe_transfer_id',
            'created_at', 'processed_at'
        ]
