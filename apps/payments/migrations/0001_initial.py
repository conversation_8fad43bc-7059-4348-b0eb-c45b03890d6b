# Generated by Django 5.2 on 2025-06-04 01:44

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("bookings", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Payment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "payment_type",
                    models.CharField(
                        choices=[
                            ("booking", "Booking Payment"),
                            ("security_deposit", "Security Deposit"),
                            ("additional_charges", "Additional Charges"),
                        ],
                        default="booking",
                        max_length=20,
                        verbose_name="payment type",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="amount",
                    ),
                ),
                (
                    "currency",
                    models.Char<PERSON>ield(
                        default="USD", max_length=3, verbose_name="currency"
                    ),
                ),
                (
                    "stripe_payment_intent_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        unique=True,
                        verbose_name="Stripe Payment Intent ID",
                    ),
                ),
                (
                    "stripe_charge_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Stripe Charge ID",
                    ),
                ),
                (
                    "stripe_customer_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Stripe Customer ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("succeeded", "Succeeded"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                            ("refunded", "Refunded"),
                            ("partially_refunded", "Partially Refunded"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="status",
                    ),
                ),
                (
                    "failure_reason",
                    models.TextField(
                        blank=True,
                        help_text="Reason for payment failure",
                        verbose_name="failure reason",
                    ),
                ),
                (
                    "platform_fee",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=8,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="platform fee",
                    ),
                ),
                (
                    "host_payout",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="host payout",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payments",
                        to="bookings.booking",
                    ),
                ),
                (
                    "payer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payments_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Payment",
                "verbose_name_plural": "Payments",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Payout",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="payout amount",
                    ),
                ),
                (
                    "currency",
                    models.CharField(
                        default="USD", max_length=3, verbose_name="currency"
                    ),
                ),
                (
                    "stripe_transfer_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        unique=True,
                        verbose_name="Stripe Transfer ID",
                    ),
                ),
                (
                    "stripe_account_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Stripe Connected Account ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("paid", "Paid"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="status",
                    ),
                ),
                (
                    "failure_reason",
                    models.TextField(blank=True, verbose_name="failure reason"),
                ),
                (
                    "scheduled_payout_date",
                    models.DateField(
                        help_text="Date when payout is scheduled to be processed",
                        verbose_name="scheduled payout date",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payouts",
                        to="bookings.booking",
                    ),
                ),
                (
                    "host",
                    models.ForeignKey(
                        limit_choices_to={"role": "host"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payouts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Payout",
                "verbose_name_plural": "Payouts",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Refund",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="refund amount",
                    ),
                ),
                (
                    "reason",
                    models.CharField(
                        choices=[
                            ("guest_cancellation", "Guest Cancellation"),
                            ("host_cancellation", "Host Cancellation"),
                            ("dispute", "Dispute"),
                            ("duplicate", "Duplicate Payment"),
                            ("fraudulent", "Fraudulent"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                        verbose_name="refund reason",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="refund description"),
                ),
                (
                    "stripe_refund_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        unique=True,
                        verbose_name="Stripe Refund ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("succeeded", "Succeeded"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="status",
                    ),
                ),
                (
                    "failure_reason",
                    models.TextField(blank=True, verbose_name="failure reason"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "initiated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="initiated_refunds",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "payment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="refunds",
                        to="payments.payment",
                    ),
                ),
            ],
            options={
                "verbose_name": "Refund",
                "verbose_name_plural": "Refunds",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="StripeWebhookEvent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "stripe_event_id",
                    models.CharField(
                        max_length=255, unique=True, verbose_name="Stripe Event ID"
                    ),
                ),
                (
                    "event_type",
                    models.CharField(max_length=100, verbose_name="event type"),
                ),
                (
                    "processed",
                    models.BooleanField(default=False, verbose_name="processed"),
                ),
                (
                    "data",
                    models.JSONField(
                        help_text="Raw webhook event data from Stripe",
                        verbose_name="event data",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Stripe Webhook Event",
                "verbose_name_plural": "Stripe Webhook Events",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["stripe_event_id"],
                        name="payments_st_stripe__a444ea_idx",
                    ),
                    models.Index(
                        fields=["event_type"], name="payments_st_event_t_802e5e_idx"
                    ),
                    models.Index(
                        fields=["processed"], name="payments_st_process_0ea60a_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="payments_st_created_3ef7e8_idx"
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="payment",
            index=models.Index(
                fields=["booking"], name="payments_pa_booking_8fd3de_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="payment",
            index=models.Index(fields=["payer"], name="payments_pa_payer_i_fc2b89_idx"),
        ),
        migrations.AddIndex(
            model_name="payment",
            index=models.Index(fields=["status"], name="payments_pa_status_7ad4af_idx"),
        ),
        migrations.AddIndex(
            model_name="payment",
            index=models.Index(
                fields=["stripe_payment_intent_id"],
                name="payments_pa_stripe__6fe52c_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="payment",
            index=models.Index(
                fields=["created_at"], name="payments_pa_created_b8a300_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="payout",
            index=models.Index(fields=["host"], name="payments_pa_host_id_7a6ef3_idx"),
        ),
        migrations.AddIndex(
            model_name="payout",
            index=models.Index(
                fields=["booking"], name="payments_pa_booking_db5cf8_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="payout",
            index=models.Index(fields=["status"], name="payments_pa_status_d49350_idx"),
        ),
        migrations.AddIndex(
            model_name="payout",
            index=models.Index(
                fields=["scheduled_payout_date"], name="payments_pa_schedul_bcf516_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="payout",
            index=models.Index(
                fields=["created_at"], name="payments_pa_created_465e24_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="refund",
            index=models.Index(
                fields=["payment"], name="payments_re_payment_0f5ef3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="refund",
            index=models.Index(fields=["status"], name="payments_re_status_715c3a_idx"),
        ),
        migrations.AddIndex(
            model_name="refund",
            index=models.Index(
                fields=["stripe_refund_id"], name="payments_re_stripe__bc040b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="refund",
            index=models.Index(
                fields=["created_at"], name="payments_re_created_d6a5d3_idx"
            ),
        ),
    ]
