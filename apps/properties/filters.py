"""
Filters for the properties app.
"""

import django_filters
from django.db import models
from .models import Property


class PropertyFilter(django_filters.FilterSet):
    """
    Filter set for Property model with advanced filtering options.
    """
    
    # Location filters
    city = django_filters.CharFilter(lookup_expr='icontains')
    state = django_filters.CharFilter(lookup_expr='icontains')
    country = django_filters.CharFilter(lookup_expr='icontains')
    
    # Price filters
    min_price = django_filters.NumberFilter(field_name='daily_price', lookup_expr='gte')
    max_price = django_filters.NumberFilter(field_name='daily_price', lookup_expr='lte')
    price_range = django_filters.RangeFilter(field_name='daily_price')
    
    # Property details filters
    bedrooms = django_filters.NumberFilter()
    min_bedrooms = django_filters.NumberFilter(field_name='bedrooms', lookup_expr='gte')
    bathrooms = django_filters.NumberFilter()
    min_bathrooms = django_filters.NumberFilter(field_name='bathrooms', lookup_expr='gte')
    
    # Guest capacity
    guests = django_filters.NumberFilter(field_name='max_guests', lookup_expr='gte')
    max_guests = django_filters.NumberFilter(field_name='max_guests')
    
    # Property type
    property_type = django_filters.ChoiceFilter(choices=Property.PropertyType.choices)
    property_types = django_filters.MultipleChoiceFilter(
        field_name='property_type',
        choices=Property.PropertyType.choices
    )
    
    # Rating filter
    min_rating = django_filters.NumberFilter(field_name='average_rating', lookup_expr='gte')
    
    # Amenities filter (JSON field)
    amenities = django_filters.CharFilter(method='filter_amenities')
    
    # Instant book
    instant_book = django_filters.BooleanFilter(field_name='is_instant_book')
    
    # Date range availability (custom method)
    available_from = django_filters.DateFilter(method='filter_available_from')
    available_to = django_filters.DateFilter(method='filter_available_to')
    
    class Meta:
        model = Property
        fields = [
            'city', 'state', 'country', 'property_type', 'bedrooms', 'bathrooms',
            'max_guests', 'is_instant_book'
        ]
    
    def filter_amenities(self, queryset, name, value):
        """Filter properties that have specific amenities."""
        if value:
            amenities_list = [amenity.strip() for amenity in value.split(',')]
            for amenity in amenities_list:
                queryset = queryset.filter(amenities__icontains=amenity)
        return queryset
    
    def filter_available_from(self, queryset, name, value):
        """Filter properties available from a specific date."""
        if value:
            # Properties that have availability records for the date and are available
            # or properties that don't have availability records (default available)
            from django.db.models import Q, Exists, OuterRef
            from .models import Availability
            
            has_availability = Availability.objects.filter(
                property=OuterRef('pk'),
                date=value
            )
            
            queryset = queryset.filter(
                Q(
                    # Has availability record and is available
                    Exists(has_availability.filter(is_available=True))
                ) | Q(
                    # No availability record (default available)
                    ~Exists(has_availability)
                )
            )
        return queryset
    
    def filter_available_to(self, queryset, name, value):
        """Filter properties available until a specific date."""
        if value:
            from django.db.models import Q, Exists, OuterRef
            from .models import Availability
            
            has_availability = Availability.objects.filter(
                property=OuterRef('pk'),
                date=value
            )
            
            queryset = queryset.filter(
                Q(
                    Exists(has_availability.filter(is_available=True))
                ) | Q(
                    ~Exists(has_availability)
                )
            )
        return queryset
