"""
Serializers for the properties app.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Property, PropertyImage, Availability

User = get_user_model()


class PropertyImageSerializer(serializers.ModelSerializer):
    """
    Serializer for property images.
    """
    class Meta:
        model = PropertyImage
        fields = ['id', 'image', 'caption', 'order', 'is_primary']
        read_only_fields = ['id']


class PropertyListSerializer(serializers.ModelSerializer):
    """
    Serializer for property list view (minimal data).
    """
    host_name = serializers.CharField(source='host.get_full_name', read_only=True)
    primary_image = serializers.SerializerMethodField()
    
    class Meta:
        model = Property
        fields = [
            'id', 'title', 'slug', 'property_type', 'city', 'state', 'country',
            'latitude', 'longitude', 'bedrooms', 'bathrooms', 'max_guests',
            'daily_price', 'average_rating', 'review_count', 'host_name',
            'primary_image', 'created_at'
        ]
        read_only_fields = ['id', 'slug', 'average_rating', 'review_count', 'created_at']
    
    def get_primary_image(self, obj):
        """Get the primary image URL."""
        primary_image = obj.images.filter(is_primary=True).first()
        if primary_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(primary_image.image.url)
        return None


class PropertyDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for property detail view (full data).
    """
    host_name = serializers.CharField(source='host.get_full_name', read_only=True)
    host_avatar = serializers.ImageField(source='host.avatar', read_only=True)
    images = PropertyImageSerializer(many=True, read_only=True)
    
    class Meta:
        model = Property
        fields = [
            'id', 'title', 'slug', 'description', 'property_type',
            'latitude', 'longitude', 'address', 'city', 'state', 'country', 'postal_code',
            'bedrooms', 'bathrooms', 'max_guests', 'square_feet',
            'daily_price', 'cleaning_fee', 'security_deposit',
            'amenities', 'house_rules', 'minimum_stay', 'maximum_stay',
            'check_in_time', 'check_out_time', 'status', 'is_instant_book',
            'average_rating', 'review_count', 'host_name', 'host_avatar',
            'images', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'slug', 'average_rating', 'review_count',
            'created_at', 'updated_at'
        ]


class PropertyCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating properties.
    """
    images = PropertyImageSerializer(many=True, read_only=True)
    uploaded_images = serializers.ListField(
        child=serializers.ImageField(),
        write_only=True,
        required=False
    )
    
    class Meta:
        model = Property
        fields = [
            'title', 'description', 'property_type',
            'latitude', 'longitude', 'address', 'city', 'state', 'country', 'postal_code',
            'bedrooms', 'bathrooms', 'max_guests', 'square_feet',
            'daily_price', 'cleaning_fee', 'security_deposit',
            'amenities', 'house_rules', 'minimum_stay', 'maximum_stay',
            'check_in_time', 'check_out_time', 'is_instant_book',
            'images', 'uploaded_images'
        ]
    
    def create(self, validated_data):
        """Create property with images."""
        uploaded_images = validated_data.pop('uploaded_images', [])
        validated_data['host'] = self.context['request'].user
        property_instance = Property.objects.create(**validated_data)
        
        # Create property images
        for i, image in enumerate(uploaded_images):
            PropertyImage.objects.create(
                property=property_instance,
                image=image,
                order=i,
                is_primary=(i == 0)  # First image is primary
            )
        
        return property_instance
    
    def update(self, instance, validated_data):
        """Update property with images."""
        uploaded_images = validated_data.pop('uploaded_images', [])
        
        # Update property fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Add new images if provided
        if uploaded_images:
            current_count = instance.images.count()
            for i, image in enumerate(uploaded_images):
                PropertyImage.objects.create(
                    property=instance,
                    image=image,
                    order=current_count + i
                )
        
        return instance
    
    def validate(self, attrs):
        """Validate property data."""
        # Ensure host can create properties
        user = self.context['request'].user
        if not user.can_host_properties():
            raise serializers.ValidationError(
                "Only KYC-verified hosts can create properties."
            )
        
        # Validate coordinates
        latitude = attrs.get('latitude')
        longitude = attrs.get('longitude')
        if latitude and longitude:
            if not (-90 <= latitude <= 90):
                raise serializers.ValidationError("Latitude must be between -90 and 90.")
            if not (-180 <= longitude <= 180):
                raise serializers.ValidationError("Longitude must be between -180 and 180.")
        
        return attrs


class AvailabilitySerializer(serializers.ModelSerializer):
    """
    Serializer for property availability.
    """
    effective_price = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        read_only=True
    )
    effective_minimum_stay = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Availability
        fields = [
            'id', 'date', 'is_available', 'price_override',
            'minimum_stay_override', 'notes', 'effective_price',
            'effective_minimum_stay'
        ]
        read_only_fields = ['id']
    
    def validate_date(self, value):
        """Validate that date is not in the past."""
        from django.utils import timezone
        if value < timezone.now().date():
            raise serializers.ValidationError("Cannot set availability for past dates.")
        return value
