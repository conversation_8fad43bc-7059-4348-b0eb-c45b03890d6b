# Generated by Django 5.2 on 2025-06-04 01:44

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Property",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="title")),
                (
                    "slug",
                    models.SlugField(
                        blank=True, max_length=220, unique=True, verbose_name="slug"
                    ),
                ),
                ("description", models.TextField(verbose_name="description")),
                (
                    "property_type",
                    models.CharField(
                        choices=[
                            ("apartment", "Apartment"),
                            ("house", "House"),
                            ("condo", "Condo"),
                            ("villa", "Villa"),
                            ("studio", "Studio"),
                            ("loft", "Loft"),
                            ("townhouse", "Townhouse"),
                        ],
                        max_length=20,
                        verbose_name="property type",
                    ),
                ),
                (
                    "latitude",
                    models.DecimalField(
                        decimal_places=6,
                        help_text="Latitude coordinate",
                        max_digits=9,
                        verbose_name="latitude",
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        decimal_places=6,
                        help_text="Longitude coordinate",
                        max_digits=9,
                        verbose_name="longitude",
                    ),
                ),
                ("address", models.TextField(verbose_name="address")),
                ("city", models.CharField(max_length=100, verbose_name="city")),
                ("state", models.CharField(max_length=100, verbose_name="state")),
                ("country", models.CharField(max_length=100, verbose_name="country")),
                (
                    "postal_code",
                    models.CharField(max_length=20, verbose_name="postal code"),
                ),
                (
                    "bedrooms",
                    models.PositiveIntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(20),
                        ],
                        verbose_name="bedrooms",
                    ),
                ),
                (
                    "bathrooms",
                    models.DecimalField(
                        decimal_places=1,
                        max_digits=3,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(20),
                        ],
                        verbose_name="bathrooms",
                    ),
                ),
                (
                    "max_guests",
                    models.PositiveIntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(50),
                        ],
                        verbose_name="maximum guests",
                    ),
                ),
                (
                    "square_feet",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="square feet"
                    ),
                ),
                (
                    "daily_price",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="daily price",
                    ),
                ),
                (
                    "cleaning_fee",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=8,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="cleaning fee",
                    ),
                ),
                (
                    "security_deposit",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="security deposit",
                    ),
                ),
                (
                    "amenities",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of amenities available at the property",
                        verbose_name="amenities",
                    ),
                ),
                (
                    "house_rules",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of house rules for guests",
                        verbose_name="house rules",
                    ),
                ),
                (
                    "minimum_stay",
                    models.PositiveIntegerField(
                        default=1,
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="minimum stay (nights)",
                    ),
                ),
                (
                    "maximum_stay",
                    models.PositiveIntegerField(
                        default=365,
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="maximum stay (nights)",
                    ),
                ),
                (
                    "check_in_time",
                    models.TimeField(default="15:00", verbose_name="check-in time"),
                ),
                (
                    "check_out_time",
                    models.TimeField(default="11:00", verbose_name="check-out time"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("suspended", "Suspended"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="status",
                    ),
                ),
                (
                    "is_instant_book",
                    models.BooleanField(
                        default=False,
                        help_text="Allow guests to book instantly without host approval",
                        verbose_name="instant book",
                    ),
                ),
                (
                    "average_rating",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Average rating from reviews",
                        max_digits=3,
                        verbose_name="average rating",
                    ),
                ),
                (
                    "review_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Total number of reviews",
                        verbose_name="review count",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "host",
                    models.ForeignKey(
                        limit_choices_to={"role": "host"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="properties",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Property",
                "verbose_name_plural": "Properties",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Availability",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(verbose_name="date")),
                (
                    "is_available",
                    models.BooleanField(default=True, verbose_name="available"),
                ),
                (
                    "price_override",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Override daily price for this specific date",
                        max_digits=10,
                        null=True,
                        verbose_name="price override",
                    ),
                ),
                (
                    "minimum_stay_override",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Override minimum stay for this specific date",
                        null=True,
                        verbose_name="minimum stay override",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Internal notes about availability",
                        verbose_name="notes",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="availability",
                        to="properties.property",
                    ),
                ),
            ],
            options={
                "verbose_name": "Availability",
                "verbose_name_plural": "Availability",
                "ordering": ["date"],
            },
        ),
        migrations.CreateModel(
            name="PropertyImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        upload_to="properties/images/", verbose_name="image"
                    ),
                ),
                (
                    "caption",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="caption"
                    ),
                ),
                ("order", models.PositiveIntegerField(default=0, verbose_name="order")),
                (
                    "is_primary",
                    models.BooleanField(default=False, verbose_name="primary image"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="images",
                        to="properties.property",
                    ),
                ),
            ],
            options={
                "verbose_name": "Property Image",
                "verbose_name_plural": "Property Images",
                "ordering": ["order", "created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="property",
            index=models.Index(fields=["status"], name="properties__status_6427a0_idx"),
        ),
        migrations.AddIndex(
            model_name="property",
            index=models.Index(
                fields=["property_type"], name="properties__propert_5c7790_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="property",
            index=models.Index(
                fields=["city", "state"], name="properties__city_fe3b3f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="property",
            index=models.Index(
                fields=["daily_price"], name="properties__daily_p_f1a588_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="property",
            index=models.Index(
                fields=["created_at"], name="properties__created_72ecc3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="property",
            index=models.Index(
                fields=["latitude", "longitude"], name="properties__latitud_6eb2b0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="availability",
            index=models.Index(
                fields=["property", "date"], name="properties__propert_a4151b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="availability",
            index=models.Index(
                fields=["date", "is_available"], name="properties__date_0eada4_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="availability",
            unique_together={("property", "date")},
        ),
        migrations.AddIndex(
            model_name="propertyimage",
            index=models.Index(
                fields=["property", "order"], name="properties__propert_94c164_idx"
            ),
        ),
    ]
