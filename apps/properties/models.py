"""
Property models for HomeSwap Platform.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.utils.text import slugify
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid

User = get_user_model()


class Property(models.Model):
    """
    Property model with PostGIS location support.
    """
    
    class PropertyType(models.TextChoices):
        APARTMENT = 'apartment', _('Apartment')
        HOUSE = 'house', _('House')
        CONDO = 'condo', _('Condo')
        VILLA = 'villa', _('Villa')
        STUDIO = 'studio', _('Studio')
        LOFT = 'loft', _('Loft')
        TOWNHOUSE = 'townhouse', _('Townhouse')
    
    class Status(models.TextChoices):
        DRAFT = 'draft', _('Draft')
        ACTIVE = 'active', _('Active')
        INACTIVE = 'inactive', _('Inactive')
        SUSPENDED = 'suspended', _('Suspended')
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    host = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='properties',
        limit_choices_to={'role': 'host'}
    )
    title = models.CharField(_('title'), max_length=200)
    slug = models.SlugField(_('slug'), max_length=220, unique=True, blank=True)
    description = models.TextField(_('description'))
    property_type = models.CharField(
        _('property type'),
        max_length=20,
        choices=PropertyType.choices
    )
    
    # Location coordinates
    latitude = models.DecimalField(
        _('latitude'),
        max_digits=9,
        decimal_places=6,
        help_text=_('Latitude coordinate')
    )
    longitude = models.DecimalField(
        _('longitude'),
        max_digits=9,
        decimal_places=6,
        help_text=_('Longitude coordinate')
    )
    address = models.TextField(_('address'))
    city = models.CharField(_('city'), max_length=100)
    state = models.CharField(_('state'), max_length=100)
    country = models.CharField(_('country'), max_length=100)
    postal_code = models.CharField(_('postal code'), max_length=20)
    
    # Property details
    bedrooms = models.PositiveIntegerField(
        _('bedrooms'),
        validators=[MinValueValidator(0), MaxValueValidator(20)]
    )
    bathrooms = models.DecimalField(
        _('bathrooms'),
        max_digits=3,
        decimal_places=1,
        validators=[MinValueValidator(0), MaxValueValidator(20)]
    )
    max_guests = models.PositiveIntegerField(
        _('maximum guests'),
        validators=[MinValueValidator(1), MaxValueValidator(50)]
    )
    square_feet = models.PositiveIntegerField(
        _('square feet'),
        blank=True,
        null=True
    )
    
    # Pricing
    daily_price = models.DecimalField(
        _('daily price'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    cleaning_fee = models.DecimalField(
        _('cleaning fee'),
        max_digits=8,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)]
    )
    security_deposit = models.DecimalField(
        _('security deposit'),
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)]
    )
    
    # Amenities (JSON field for flexibility)
    amenities = models.JSONField(
        _('amenities'),
        default=list,
        blank=True,
        help_text=_('List of amenities available at the property')
    )
    
    # House rules (JSON field for flexibility)
    house_rules = models.JSONField(
        _('house rules'),
        default=list,
        blank=True,
        help_text=_('List of house rules for guests')
    )
    
    # Availability
    minimum_stay = models.PositiveIntegerField(
        _('minimum stay (nights)'),
        default=1,
        validators=[MinValueValidator(1)]
    )
    maximum_stay = models.PositiveIntegerField(
        _('maximum stay (nights)'),
        default=365,
        validators=[MinValueValidator(1)]
    )
    
    # Check-in/out times
    check_in_time = models.TimeField(_('check-in time'), default='15:00')
    check_out_time = models.TimeField(_('check-out time'), default='11:00')
    
    # Status and metadata
    status = models.CharField(
        _('status'),
        max_length=20,
        choices=Status.choices,
        default=Status.DRAFT
    )
    is_instant_book = models.BooleanField(
        _('instant book'),
        default=False,
        help_text=_('Allow guests to book instantly without host approval')
    )
    
    # Ratings (will be updated via signals)
    average_rating = models.DecimalField(
        _('average rating'),
        max_digits=3,
        decimal_places=2,
        default=0,
        help_text=_('Average rating from reviews')
    )
    review_count = models.PositiveIntegerField(
        _('review count'),
        default=0,
        help_text=_('Total number of reviews')
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Property')
        verbose_name_plural = _('Properties')
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['property_type']),
            models.Index(fields=['city', 'state']),
            models.Index(fields=['daily_price']),
            models.Index(fields=['created_at']),
            models.Index(fields=['latitude', 'longitude']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
            # Ensure slug uniqueness
            counter = 1
            original_slug = self.slug
            while Property.objects.filter(slug=self.slug).exists():
                self.slug = f"{original_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)


class PropertyImage(models.Model):
    """
    Property images with ordering support.
    """
    property = models.ForeignKey(
        Property,
        on_delete=models.CASCADE,
        related_name='images'
    )
    image = models.ImageField(
        _('image'),
        upload_to='properties/images/'
    )
    caption = models.CharField(
        _('caption'),
        max_length=200,
        blank=True
    )
    order = models.PositiveIntegerField(
        _('order'),
        default=0
    )
    is_primary = models.BooleanField(
        _('primary image'),
        default=False
    )
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = _('Property Image')
        verbose_name_plural = _('Property Images')
        ordering = ['order', 'created_at']
        indexes = [
            models.Index(fields=['property', 'order']),
        ]
    
    def __str__(self):
        return f"{self.property.title} - Image {self.order}"


class Availability(models.Model):
    """
    Property availability calendar with optimistic locking.
    One row per day for efficient querying and booking management.
    """
    property = models.ForeignKey(
        Property,
        on_delete=models.CASCADE,
        related_name='availability'
    )
    date = models.DateField(_('date'))
    is_available = models.BooleanField(_('available'), default=True)
    price_override = models.DecimalField(
        _('price override'),
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text=_('Override daily price for this specific date')
    )
    minimum_stay_override = models.PositiveIntegerField(
        _('minimum stay override'),
        blank=True,
        null=True,
        help_text=_('Override minimum stay for this specific date')
    )
    notes = models.TextField(
        _('notes'),
        blank=True,
        help_text=_('Internal notes about availability')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Availability')
        verbose_name_plural = _('Availability')
        unique_together = ['property', 'date']
        indexes = [
            models.Index(fields=['property', 'date']),
            models.Index(fields=['date', 'is_available']),
        ]
        ordering = ['date']
    
    def __str__(self):
        status = "Available" if self.is_available else "Unavailable"
        return f"{self.property.title} - {self.date} ({status})"
    
    @property
    def effective_price(self):
        """Get the effective price for this date."""
        return self.price_override or self.property.daily_price
    
    @property
    def effective_minimum_stay(self):
        """Get the effective minimum stay for this date."""
        return self.minimum_stay_override or self.property.minimum_stay
