"""
Views for the properties app.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

from .models import Property, Availability
from .serializers import (
    PropertyListSerializer,
    PropertyDetailSerializer,
    PropertyCreateUpdateSerializer,
    AvailabilitySerializer
)
from .filters import PropertyFilter


class PropertyViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing properties.
    """
    queryset = Property.objects.filter(status='active').select_related('host').prefetch_related('images')
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = PropertyFilter
    search_fields = ['title', 'description', 'city', 'state', 'country']
    ordering_fields = ['daily_price', 'average_rating', 'created_at']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return PropertyListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return PropertyCreateUpdateSerializer
        return PropertyDetailSerializer
    
    def get_permissions(self):
        """Set permissions based on action."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.AllowAny]
        return [permission() for permission in permission_classes]
    
    def get_queryset(self):
        """Filter queryset based on user and action."""
        queryset = super().get_queryset()
        
        if self.action == 'my_properties':
            # Only return user's own properties
            return queryset.filter(host=self.request.user)
        
        return queryset
    
    @extend_schema(
        summary="List properties",
        description="Get a list of active properties with filtering and search.",
        parameters=[
            OpenApiParameter(
                name='city',
                type=OpenApiTypes.STR,
                description='Filter by city'
            ),
            OpenApiParameter(
                name='min_price',
                type=OpenApiTypes.NUMBER,
                description='Minimum daily price'
            ),
            OpenApiParameter(
                name='max_price',
                type=OpenApiTypes.NUMBER,
                description='Maximum daily price'
            ),
            OpenApiParameter(
                name='bedrooms',
                type=OpenApiTypes.INT,
                description='Number of bedrooms'
            ),
            OpenApiParameter(
                name='guests',
                type=OpenApiTypes.INT,
                description='Maximum number of guests'
            ),
        ]
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @extend_schema(
        summary="Create property",
        description="Create a new property listing. Only KYC-verified hosts can create properties."
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    
    @extend_schema(
        summary="Get my properties",
        description="Get properties owned by the authenticated user."
    )
    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def my_properties(self, request):
        """Get properties owned by the authenticated user."""
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PropertyListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)
        
        serializer = PropertyListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)
    
    @extend_schema(
        summary="Search properties by location",
        description="Search properties within a radius of given coordinates.",
        parameters=[
            OpenApiParameter(
                name='latitude',
                type=OpenApiTypes.NUMBER,
                required=True,
                description='Latitude coordinate'
            ),
            OpenApiParameter(
                name='longitude',
                type=OpenApiTypes.NUMBER,
                required=True,
                description='Longitude coordinate'
            ),
            OpenApiParameter(
                name='radius',
                type=OpenApiTypes.NUMBER,
                description='Search radius in kilometers (default: 10)'
            ),
        ]
    )
    @action(detail=False, methods=['get'])
    def search_by_location(self, request):
        """Search properties by geographic location."""
        try:
            latitude = float(request.query_params.get('latitude'))
            longitude = float(request.query_params.get('longitude'))
            radius = float(request.query_params.get('radius', 10))  # Default 10km
        except (TypeError, ValueError):
            return Response(
                {'error': 'Invalid latitude, longitude, or radius parameters.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Simple distance calculation (for production, use PostGIS)
        # This is a basic implementation using Haversine formula approximation
        from math import radians, cos, sin, asin, sqrt
        
        def haversine_distance(lat1, lon1, lat2, lon2):
            """Calculate distance between two points using Haversine formula."""
            lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
            return 2 * asin(sqrt(a)) * 6371  # Earth radius in km
        
        # Filter properties within radius
        properties = []
        for prop in self.get_queryset():
            if prop.latitude and prop.longitude:
                distance = haversine_distance(
                    latitude, longitude,
                    float(prop.latitude), float(prop.longitude)
                )
                if distance <= radius:
                    properties.append(prop)
        
        # Paginate results
        page = self.paginate_queryset(properties)
        if page is not None:
            serializer = PropertyListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)
        
        serializer = PropertyListSerializer(properties, many=True, context={'request': request})
        return Response(serializer.data)
    
    @extend_schema(
        summary="Get property availability",
        description="Get availability calendar for a property."
    )
    @action(detail=True, methods=['get'])
    def availability(self, request, pk=None):
        """Get availability for a property."""
        property_instance = self.get_object()
        availability = Availability.objects.filter(property=property_instance).order_by('date')
        
        # Filter by date range if provided
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        
        if start_date:
            availability = availability.filter(date__gte=start_date)
        if end_date:
            availability = availability.filter(date__lte=end_date)
        
        serializer = AvailabilitySerializer(availability, many=True)
        return Response(serializer.data)
    
    @extend_schema(
        summary="Update property availability",
        description="Update availability for specific dates."
    )
    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def update_availability(self, request, pk=None):
        """Update availability for a property."""
        property_instance = self.get_object()
        
        # Check if user owns the property
        if property_instance.host != request.user:
            return Response(
                {'error': 'You can only update availability for your own properties.'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = AvailabilitySerializer(data=request.data, many=True)
        if serializer.is_valid():
            # Update or create availability records
            for item in serializer.validated_data:
                Availability.objects.update_or_create(
                    property=property_instance,
                    date=item['date'],
                    defaults=item
                )
            return Response({'message': 'Availability updated successfully.'})
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
