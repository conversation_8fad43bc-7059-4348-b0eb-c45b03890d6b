"""
Admin configuration for properties app.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from .models import Property, PropertyImage, Availability


class PropertyImageInline(admin.TabularInline):
    """
    Inline admin for property images.
    """
    model = PropertyImage
    extra = 1
    fields = ['image', 'caption', 'order', 'is_primary']
    readonly_fields = ['image_preview']
    
    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="max-height: 100px; max-width: 100px;" />',
                obj.image.url
            )
        return "No image"
    image_preview.short_description = "Preview"


@admin.register(Property)
class PropertyAdmin(admin.ModelAdmin):
    """
    Admin for Property model with Django 5 facet filters.
    """
    list_display = [
        'title', 'host', 'property_type', 'city', 'state',
        'daily_price', 'status', 'average_rating', 'review_count',
        'is_instant_book', 'created_at'
    ]
    list_filter = [
        'status', 'property_type', 'is_instant_book',
        'city', 'state', 'country', 'created_at'
    ]
    search_fields = [
        'title', 'description', 'host__email', 'host__first_name',
        'host__last_name', 'city', 'state', 'country'
    ]
    readonly_fields = [
        'id', 'slug', 'average_rating', 'review_count',
        'created_at', 'updated_at'
    ]
    
    # Django 5 facet filters for fast moderation
    list_facets = ['status', 'property_type', 'is_instant_book']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': (
                'title', 'slug', 'description', 'property_type', 'status'
            )
        }),
        (_('Location'), {
            'fields': (
                'latitude', 'longitude', 'address', 'city',
                'state', 'country', 'postal_code'
            )
        }),
        (_('Property Details'), {
            'fields': (
                'bedrooms', 'bathrooms', 'max_guests', 'square_feet'
            )
        }),
        (_('Pricing'), {
            'fields': (
                'daily_price', 'cleaning_fee', 'security_deposit'
            )
        }),
        (_('Amenities & Rules'), {
            'fields': ('amenities', 'house_rules'),
            'classes': ('collapse',)
        }),
        (_('Booking Settings'), {
            'fields': (
                'minimum_stay', 'maximum_stay', 'check_in_time',
                'check_out_time', 'is_instant_book'
            )
        }),
        (_('Host & Ratings'), {
            'fields': ('host', 'average_rating', 'review_count'),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [PropertyImageInline]
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('host')


@admin.register(PropertyImage)
class PropertyImageAdmin(admin.ModelAdmin):
    """
    Admin for PropertyImage model.
    """
    list_display = ['property', 'caption', 'order', 'is_primary', 'image_preview']
    list_filter = ['is_primary', 'property__property_type']
    search_fields = ['property__title', 'caption']
    list_editable = ['order', 'is_primary']
    
    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="max-height: 50px; max-width: 50px;" />',
                obj.image.url
            )
        return "No image"
    image_preview.short_description = "Preview"


@admin.register(Availability)
class AvailabilityAdmin(admin.ModelAdmin):
    """
    Admin for Availability model.
    """
    list_display = [
        'property', 'date', 'is_available', 'price_override',
        'minimum_stay_override', 'effective_price'
    ]
    list_filter = ['is_available', 'date', 'property__city']
    search_fields = ['property__title', 'notes']
    date_hierarchy = 'date'
    list_editable = ['is_available', 'price_override']
    
    # Django 5 facet filters
    list_facets = ['is_available']
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('property')
    
    def effective_price(self, obj):
        """Display effective price."""
        return f"${obj.effective_price()}"
    effective_price.short_description = "Effective Price"
